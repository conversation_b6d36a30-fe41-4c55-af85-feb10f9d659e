#!/usr/bin/env python3
"""
性能优化测试脚本

测试批量加载和指标计算的性能提升
"""
import sys
import time
import requests
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_indicator_ranking_performance():
    """测试指标排行性能"""
    print("🧪 测试指标排行性能...")
    
    base_url = "http://localhost:8000"
    
    try:
        # 测试RSI排行
        start_time = time.time()
        response = requests.get(f"{base_url}/api/quantitative/indicators/ranking?indicator=RSI&limit=100", timeout=30)
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            elapsed = end_time - start_time
            
            print(f"✅ RSI排行计算成功")
            print(f"  📊 返回股票数: {len(data.get('rankings', []))}")
            print(f"  ⏱ 耗时: {elapsed:.2f} 秒")
            print(f"  🚀 处理速度: {len(data.get('rankings', [])) / elapsed:.1f} 股票/秒")
            
            # 检查数据质量
            if data.get('rankings'):
                first_item = data['rankings'][0]
                print(f"  📈 最高RSI: {first_item.get('symbol')} = {first_item.get('value', 0):.2f}")
            
            return True, elapsed
        else:
            print(f"❌ RSI排行请求失败: {response.status_code}")
            return False, 0
            
    except Exception as e:
        print(f"❌ 指标排行测试失败: {e}")
        return False, 0


def test_pattern_screening_performance():
    """测试形态筛选性能"""
    print("\n🧪 测试形态筛选性能...")
    
    base_url = "http://localhost:8000"
    
    try:
        # 测试形态筛选
        start_time = time.time()
        response = requests.get(
            f"{base_url}/api/quantitative/patterns/screening?signal_type=BULLISH&min_confidence=0.6&limit=20", 
            timeout=60
        )
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            elapsed = end_time - start_time
            
            print(f"✅ 形态筛选成功")
            print(f"  📊 分析股票数: {data.get('total_analyzed', 0)}")
            print(f"  🎯 匹配股票数: {data.get('total_matched', 0)}")
            print(f"  ⏱ 耗时: {elapsed:.2f} 秒")
            
            if data.get('total_analyzed', 0) > 0:
                print(f"  🚀 分析速度: {data.get('total_analyzed', 0) / elapsed:.1f} 股票/秒")
            
            # 检查结果质量
            if data.get('results'):
                first_result = data['results'][0]
                print(f"  📈 最佳匹配: {first_result.get('symbol')} ({first_result.get('pattern_count', 0)} 个形态)")
            
            return True, elapsed
        else:
            print(f"❌ 形态筛选请求失败: {response.status_code}")
            return False, 0
            
    except Exception as e:
        print(f"❌ 形态筛选测试失败: {e}")
        return False, 0


def test_stock_indicators_performance():
    """测试股票指标性能"""
    print("\n🧪 测试股票指标性能...")
    
    base_url = "http://localhost:8000"
    test_symbols = ["000001", "000002", "600000", "600036", "000858"]
    
    total_time = 0
    success_count = 0
    
    for symbol in test_symbols:
        try:
            start_time = time.time()
            response = requests.get(f"{base_url}/api/quantitative/stocks/{symbol}/indicators", timeout=10)
            end_time = time.time()
            
            elapsed = end_time - start_time
            total_time += elapsed
            
            if response.status_code == 200:
                data = response.json()
                success_count += 1
                
                indicators_count = len(data.get('indicators', []))
                print(f"  ✅ {symbol}: {indicators_count} 个指标, {elapsed:.2f}s")
            else:
                print(f"  ❌ {symbol}: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ {symbol}: {e}")
    
    if success_count > 0:
        avg_time = total_time / success_count
        print(f"\n📊 股票指标性能统计:")
        print(f"  成功率: {success_count}/{len(test_symbols)} ({success_count/len(test_symbols)*100:.1f}%)")
        print(f"  平均耗时: {avg_time:.2f} 秒/股票")
        print(f"  总耗时: {total_time:.2f} 秒")
        
        return True, avg_time
    else:
        print("❌ 所有股票指标请求都失败了")
        return False, 0


def compare_with_baseline():
    """与基准性能对比"""
    print("\n📊 性能对比分析")
    print("="*50)
    
    # 基准性能数据（优化前）
    baseline = {
        "indicator_ranking": 5.0,  # 5秒处理100只股票
        "pattern_screening": 10.0,  # 10秒分析50只股票
        "stock_indicators": 0.5     # 0.5秒/股票
    }
    
    print("基准性能（优化前）:")
    print(f"  指标排行: {baseline['indicator_ranking']:.1f}s (100股票)")
    print(f"  形态筛选: {baseline['pattern_screening']:.1f}s (50股票)")
    print(f"  股票指标: {baseline['stock_indicators']:.1f}s/股票")
    
    return baseline


def main():
    """主函数"""
    print("🚀 性能优化测试")
    print("="*50)
    
    # 检查服务器状态
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ 服务器未运行，请先启动:")
            print("   python -m uvicorn app.main:app --reload")
            return
    except:
        print("❌ 无法连接到服务器，请先启动:")
        print("   python -m uvicorn app.main:app --reload")
        return
    
    print("✅ 服务器运行正常，开始性能测试...\n")
    
    # 获取基准性能
    baseline = compare_with_baseline()
    
    # 执行性能测试
    results = {}
    
    # 测试指标排行
    success, time_taken = test_indicator_ranking_performance()
    if success:
        results['indicator_ranking'] = time_taken
    
    # 测试形态筛选
    success, time_taken = test_pattern_screening_performance()
    if success:
        results['pattern_screening'] = time_taken
    
    # 测试股票指标
    success, time_taken = test_stock_indicators_performance()
    if success:
        results['stock_indicators'] = time_taken
    
    # 性能对比总结
    print("\n" + "="*60)
    print("🎉 性能优化总结")
    print("="*60)
    
    improvements = []
    
    for test_name, current_time in results.items():
        baseline_time = baseline.get(test_name, 0)
        if baseline_time > 0:
            improvement = (baseline_time - current_time) / baseline_time * 100
            improvements.append(improvement)
            
            status = "🚀 显著提升" if improvement > 50 else "✅ 有所提升" if improvement > 0 else "⚠️ 需要优化"
            
            print(f"\n{test_name.replace('_', ' ').title()}:")
            print(f"  优化前: {baseline_time:.2f}s")
            print(f"  优化后: {current_time:.2f}s")
            print(f"  提升: {improvement:+.1f}% {status}")
    
    if improvements:
        avg_improvement = sum(improvements) / len(improvements)
        print(f"\n📈 平均性能提升: {avg_improvement:.1f}%")
        
        if avg_improvement > 50:
            print("🎉 优化效果显著！")
        elif avg_improvement > 20:
            print("✅ 优化效果良好！")
        else:
            print("⚠️ 还有优化空间")
    
    print(f"\n💡 优化建议:")
    print("1. 使用批量数据加载替代逐个加载")
    print("2. 添加适当的缓存机制")
    print("3. 限制并发请求数量")
    print("4. 考虑使用数据库索引优化")


if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
