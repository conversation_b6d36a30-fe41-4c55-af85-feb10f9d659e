"""
量化分析API路由

提供股票数据、技术指标、K线图等API接口
"""
from fastapi import APIRouter, HTTPException, Query, Depends
from fastapi.responses import JSONResponse
from typing import List, Dict, Optional, Union
import pandas as pd
import numpy as np
import json
import math
from datetime import datetime, timedelta
import logging
from functools import lru_cache
import hashlib
import time

from ..strategy.services.data_service import data_service
from ..strategy.services.indicator_service import indicator_service
from ..strategy.services.market_data_service import market_data_service
from ..strategy.patterns.engine import PatternEngine

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/quantitative", tags=["quantitative"])

# 初始化形态识别引擎
pattern_engine = PatternEngine()


def safe_float(value, default=0.0):
    """安全的浮点数转换，处理NaN和无穷大值"""
    try:
        if pd.isna(value) or math.isinf(value):
            return default
        return float(value)
    except (ValueError, TypeError):
        return default


def safe_dict_values(data):
    """递归处理字典中的浮点数值，确保JSON序列化安全"""
    if isinstance(data, dict):
        return {k: safe_dict_values(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [safe_dict_values(item) for item in data]
    elif isinstance(data, (int, float)):
        return safe_float(data)
    else:
        return data


# 缓存形态识别结果
@lru_cache(maxsize=200)
def get_cached_patterns(symbol: str, data_hash: str):
    """缓存形态识别结果"""
    try:
        data = data_service.load_stock_history(symbol, limit=30)
        if data.empty:
            return {}
        return pattern_engine.detect_all_patterns(data)
    except Exception as e:
        logger.error(f"缓存形态识别失败 {symbol}: {e}")
        return {}

def get_data_hash(data: pd.DataFrame) -> str:
    """生成数据哈希值用于缓存"""
    if data.empty:
        return ""
    # 使用最后几行数据的哈希值
    last_rows = data.tail(5)
    data_str = f"{len(data)}_{last_rows.to_string()}"
    return hashlib.md5(data_str.encode()).hexdigest()[:16]

@router.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "ok", "timestamp": datetime.now().isoformat()}

@router.get("/market/overview")
async def get_market_overview():
    """获取市场概览"""
    try:
        overview = market_data_service.get_market_overview()
        return JSONResponse(content=overview)
    except Exception as e:
        logger.error(f"获取市场概览失败: {e}")
        raise HTTPException(status_code=500, detail="获取市场概览失败")

@router.get("/market/sectors")
async def get_market_sectors(limit: int = Query(20, ge=1, le=100, description="返回数量")):
    """获取板块数据"""
    try:
        sectors = market_data_service.get_sector_data(limit)
        return JSONResponse(content=sectors)
    except Exception as e:
        logger.error(f"获取板块数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取板块数据失败")

@router.get("/market/industries")
async def get_market_industries(limit: int = Query(20, ge=1, le=100, description="返回数量")):
    """获取行业数据"""
    try:
        industries = market_data_service.get_industry_data(limit)
        return JSONResponse(content=industries)
    except Exception as e:
        logger.error(f"获取行业数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取行业数据失败")

@router.get("/market/indices")
async def get_market_indices():
    """获取主要指数数据"""
    try:
        indices = market_data_service.get_indices_data()
        return JSONResponse(content=indices)
    except Exception as e:
        logger.error(f"获取指数数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取指数数据失败")

@router.get("/market/hot-stocks")
async def get_hot_stocks(limit: int = Query(10, ge=1, le=50, description="返回数量")):
    """获取热门股票"""
    try:
        hot_stocks = market_data_service.get_hot_stocks(limit)
        return JSONResponse(content=hot_stocks)
    except Exception as e:
        logger.error(f"获取热门股票失败: {e}")
        raise HTTPException(status_code=500, detail="获取热门股票失败")

@router.get("/stocks/list")
async def get_stock_list(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(50, ge=1, le=500, description="每页数量"),
    sort_by: str = Query("amount", description="排序字段: amount, volume, pct_change"),
    search: Optional[str] = Query(None, description="搜索关键词")
):
    """获取股票列表"""
    try:
        if search:
            stocks = data_service.search_stocks(search, size)
        else:
            stocks = data_service.get_top_stocks(sort_by, size * page)
            # 分页处理
            start_idx = (page - 1) * size
            end_idx = start_idx + size
            stocks = stocks[start_idx:end_idx]
        
        return JSONResponse(content={
            "stocks": stocks,
            "page": page,
            "size": len(stocks),
            "total": len(stocks)
        })
    except Exception as e:
        logger.error(f"获取股票列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取股票列表失败")

@router.get("/stocks/{symbol}/basic")
async def get_stock_basic_info(symbol: str):
    """获取股票基本信息"""
    try:
        # 获取最新数据
        stock_list = data_service.get_stock_list()
        stock_info = next((s for s in stock_list if s['symbol'] == symbol), None)
        
        if not stock_info:
            raise HTTPException(status_code=404, detail="股票不存在")
        
        return JSONResponse(content=stock_info)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取股票 {symbol} 基本信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取股票信息失败")

@router.get("/stocks/{symbol}/kline")
async def get_stock_kline(
    symbol: str,
    start_date: Optional[str] = Query(None, description="开始日期 YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD"),
    limit: int = Query(250, ge=1, le=1000, description="数据条数")
):
    """获取股票K线数据"""
    try:
        # 加载历史数据
        data = data_service.load_stock_history(symbol, start_date, end_date, limit)
        
        if data.empty:
            return JSONResponse(content={"kline": [], "symbol": symbol})
        
        # 转换为K线格式
        kline_data = []
        for _, row in data.iterrows():
            # 安全处理日期格式
            date_value = row['date']
            if pd.notna(date_value):
                if hasattr(date_value, 'strftime'):
                    date_str = date_value.strftime('%Y-%m-%d')
                else:
                    date_str = str(date_value)
            else:
                date_str = None

            kline_item = {
                "date": date_str,
                "open": safe_float(row['open']),
                "high": safe_float(row['high']),
                "low": safe_float(row['low']),
                "close": safe_float(row['close']),
                "volume": int(safe_float(row['volume'])),
                "amount": float(row['amount']) if pd.notna(row['amount']) else 0.0,
                "pct_change": float(row['pct_change']) if pd.notna(row['pct_change']) else 0.0
            }
            kline_data.append(kline_item)
        
        return JSONResponse(content={
            "symbol": symbol,
            "kline": kline_data,
            "count": len(kline_data)
        })
        
    except Exception as e:
        logger.error(f"获取股票 {symbol} K线数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取K线数据失败")

@router.get("/stocks/{symbol}/indicators")
async def get_stock_indicators(
    symbol: str,
    indicators: Optional[str] = Query(None, description="指标列表，逗号分隔"),
    start_date: Optional[str] = Query(None, description="开始日期 YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD"),
    latest_only: bool = Query(False, description="只返回最新指标")
):
    """获取股票技术指标（优先使用预计算数据）"""
    try:
        # 解析指标列表
        if indicators:
            indicator_list = [ind.strip().upper() for ind in indicators.split(',')]
        else:
            indicator_list = None  # None表示所有指标

        # 如果只需要最新指标，使用优化的方法
        if latest_only:
            latest_data = indicator_service.get_latest_indicators(symbol, indicator_list)

            if latest_data['indicators']:
                return JSONResponse(content={
                    "symbol": symbol,
                    "date": latest_data['date'],
                    "indicators": latest_data['indicators'],
                    "source": "precomputed"
                })
            else:
                return JSONResponse(content={
                    "symbol": symbol,
                    "indicators": {},
                    "message": "没有预计算的指标数据"
                })

        # 获取历史指标数据（优先从预计算数据读取）
        data = indicator_service.calculate_indicators(symbol, start_date, end_date, indicator_list)

        if data.empty:
            return JSONResponse(content={
                "indicators": [],
                "symbol": symbol,
                "message": "没有找到指标数据"
            })
        
        # 转换为JSON格式
        indicators_data = []
        for _, row in data.iterrows():
            # 安全处理日期格式
            date_value = row['date']
            if pd.notna(date_value):
                if hasattr(date_value, 'strftime'):
                    # 如果是datetime对象
                    date_str = date_value.strftime('%Y-%m-%d')
                else:
                    # 如果是字符串，直接使用
                    date_str = str(date_value)
            else:
                date_str = None

            indicator_item = {
                "date": date_str,
                "close": safe_float(row['close']) if pd.notna(row['close']) else 0.0
            }
            
            # 添加各种指标，处理不同数据类型
            for col in data.columns:
                if col not in ['symbol', 'date', 'open', 'high', 'low', 'close', 'volume', 'amount']:
                    value = row[col]
                    # 包含所有列，即使是NaN值也设为null
                    if pd.notna(value):
                        if isinstance(value, (bool, np.bool_)):
                            indicator_item[col] = bool(value)
                        elif isinstance(value, str):
                            indicator_item[col] = value
                        elif isinstance(value, (int, np.integer)):
                            indicator_item[col] = int(value)
                        elif isinstance(value, (float, np.floating)):
                            indicator_item[col] = safe_float(value)
                        else:
                            # 尝试转换为数值，失败则保持原值
                            try:
                                indicator_item[col] = safe_float(value)
                            except (ValueError, TypeError):
                                indicator_item[col] = str(value)
                    else:
                        # NaN值设为null
                        indicator_item[col] = None
            
            indicators_data.append(indicator_item)
        
        return JSONResponse(content={
            "symbol": symbol,
            "indicators": indicators_data,
            "count": len(indicators_data)
        })
        
    except Exception as e:
        logger.error(f"获取股票 {symbol} 技术指标失败: {e}")
        raise HTTPException(status_code=500, detail="获取技术指标失败")

@router.get("/stocks/{symbol}/summary")
async def get_stock_summary(symbol: str):
    """获取股票指标摘要"""
    try:
        summary = indicator_service.get_indicator_summary(symbol)
        
        if not summary:
            raise HTTPException(status_code=404, detail="股票数据不存在")
        
        return JSONResponse(content=summary)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取股票 {symbol} 摘要失败: {e}")
        raise HTTPException(status_code=500, detail="获取股票摘要失败")

@router.get("/indicators/ranking")
async def get_indicators_ranking(
    indicator: str = Query("RSI", description="指标名称"),
    limit: int = Query(50, ge=1, le=200, description="返回数量")
):
    """获取指标排行榜"""
    try:
        rankings = indicator_service.get_market_indicators_ranking(indicator, limit)

        # 安全处理排行数据
        safe_rankings = safe_dict_values(rankings)

        return JSONResponse(content={
            "indicator": indicator,
            "rankings": safe_rankings,
            "count": len(rankings)
        })
        
    except Exception as e:
        logger.error(f"获取指标 {indicator} 排行失败: {e}")
        raise HTTPException(status_code=500, detail="获取指标排行失败")

@router.get("/indicators/available")
async def get_available_indicators():
    """获取可用指标列表"""
    try:
        indicators = indicator_service.get_available_indicators()
        return JSONResponse(content={"indicators": indicators})
    except Exception as e:
        logger.error(f"获取可用指标失败: {e}")
        raise HTTPException(status_code=500, detail="获取可用指标失败")

@router.get("/indicators/latest")
async def get_latest_indicators_all():
    """获取最新日期的所有股票指标摘要"""
    try:
        from app.strategy.services.high_performance_data_service import HighPerformanceDataService
        from app.strategy.indicators.parquet_storage import ParquetIndicatorStorage

        data_service = HighPerformanceDataService()
        storage = ParquetIndicatorStorage()

        # 获取最新日期
        available_dates = data_service.get_available_dates()
        if not available_dates:
            return JSONResponse(content={
                "latest_date": None,
                "indicators": [],
                "message": "没有可用的日期数据"
            })

        latest_date = available_dates[-1]

        # 加载最新日期的指标数据
        indicators_data = storage.load_indicators_by_date(latest_date)

        if indicators_data.empty:
            return JSONResponse(content={
                "latest_date": latest_date,
                "indicators": [],
                "message": f"日期 {latest_date} 没有指标数据"
            })

        # 转换为API响应格式
        indicators_list = []
        for _, row in indicators_data.iterrows():
            indicator_dict = {
                "symbol": row.get('symbol', ''),
                "date": row.get('date', ''),
            }

            # 添加主要指标
            for col in indicators_data.columns:
                if col not in ['symbol', 'date'] and pd.api.types.is_numeric_dtype(indicators_data[col]):
                    value = row[col]
                    if not pd.isna(value):
                        indicator_dict[col] = float(value)

            indicators_list.append(indicator_dict)

        return JSONResponse(content={
            "latest_date": latest_date,
            "count": len(indicators_list),
            "indicators": indicators_list[:100],  # 限制返回数量
            "total_available": len(indicators_list)
        })

    except Exception as e:
        logger.error(f"获取最新指标数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取最新指标数据失败")

@router.post("/stocks/batch/indicators")
async def calculate_batch_indicators(
    request_data: Dict[str, Union[List[str], str]]
):
    """批量计算股票指标"""
    try:
        symbols = request_data.get('symbols', [])
        indicators = request_data.get('indicators', ['MACD', 'RSI', 'KDJ'])
        
        if not symbols:
            raise HTTPException(status_code=400, detail="股票列表不能为空")
        
        # 限制批量数量
        symbols = symbols[:50]
        
        results = await indicator_service.calculate_batch_indicators(symbols, indicators)
        
        # 转换结果格式
        batch_results = {}
        for symbol, data in results.items():
            if not data.empty:
                latest = data.iloc[-1]

                # 安全处理日期格式
                date_value = latest['date']
                if pd.notna(date_value):
                    if hasattr(date_value, 'strftime'):
                        date_str = date_value.strftime('%Y-%m-%d')
                    else:
                        date_str = str(date_value)
                else:
                    date_str = None

                batch_results[symbol] = {
                    'date': date_str,
                    'close': safe_float(latest['close']),
                    'indicators': {}
                }
                
                # 添加指标数据
                for col in data.columns:
                    if col not in ['symbol', 'date', 'open', 'high', 'low', 'close', 'volume', 'amount']:
                        if pd.notna(latest[col]):
                            batch_results[symbol]['indicators'][col] = float(latest[col])
        
        return JSONResponse(content={
            "results": batch_results,
            "count": len(batch_results)
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量计算指标失败: {e}")
        raise HTTPException(status_code=500, detail="批量计算指标失败")

@router.get("/dates/available")
async def get_available_dates():
    """获取可用交易日期"""
    try:
        dates = data_service.get_available_dates()
        return JSONResponse(content={
            "dates": dates,
            "count": len(dates),
            "latest": dates[-1] if dates else None,
            "earliest": dates[0] if dates else None
        })
    except Exception as e:
        logger.error(f"获取可用日期失败: {e}")
        raise HTTPException(status_code=500, detail="获取可用日期失败")

@router.get("/stocks/{symbol}/patterns")
async def get_stock_patterns(symbol: str):
    """获取股票K线形态识别结果"""
    try:
        # 加载股票数据 - 限制到最近30天以提升性能
        data = data_service.load_stock_history(symbol, limit=30)

        if data.empty:
            return JSONResponse(content={
                "patterns": [],
                "summary": {},
                "symbol": symbol,
                "message": "暂无数据"
            })

        # 检测K线形态 - 使用缓存
        data_hash = get_data_hash(data)
        patterns = get_cached_patterns(symbol, data_hash)

        # 构造响应数据
        pattern_detections = []
        for pattern_name, detections in patterns.items():
            for detection in detections:
                # 安全处理日期格式
                if hasattr(detection['date'], 'strftime'):
                    date_str = detection['date'].strftime('%Y-%m-%d')
                elif isinstance(detection['date'], str):
                    date_str = detection['date']
                else:
                    # 如果是索引，尝试从数据中获取对应日期
                    try:
                        if isinstance(detection['date'], (int, float)) and detection['date'] < len(data):
                            row_date = data.iloc[int(detection['date'])]['date']
                            if hasattr(row_date, 'strftime'):
                                date_str = row_date.strftime('%Y-%m-%d')
                            else:
                                date_str = str(row_date)
                        else:
                            date_str = str(detection['date'])
                    except:
                        date_str = str(detection['date'])

                pattern_detections.append({
                    'symbol': symbol,
                    'pattern_name': pattern_name,
                    'pattern_type': detection['type'],
                    'signal': detection['signal'],
                    'confidence': detection['confidence'],
                    'date': date_str,
                    'description': detection['description']
                })

        # 统计信息
        summary = {}
        for pattern in pattern_detections:
            signal = pattern['signal']
            summary[signal] = summary.get(signal, 0) + 1

        return JSONResponse(content={
            "symbol": symbol,
            "patterns": pattern_detections,
            "summary": summary,
            "total": len(pattern_detections),
            "analysis_time": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"获取股票 {symbol} K线形态失败: {e}")
        raise HTTPException(status_code=500, detail="获取K线形态失败")

@router.get("/patterns/types")
async def get_pattern_types():
    """获取所有支持的形态类型"""
    try:
        # 定义形态类型及其分类
        pattern_categories = {
            "反转形态": {
                "bullish": [
                    {"name": "engulfing", "label": "看涨吞噬", "description": "阴线后出现完全包含前一根阴线的阳线"},
                    {"name": "hammer", "label": "锤头线", "description": "下影线长、实体小的看涨信号"},
                    {"name": "dragonfly_doji", "label": "蜻蜓十字", "description": "只有下影线的十字星"},
                    {"name": "morning_star", "label": "晨星", "description": "三根K线组成的强烈看涨信号"},
                    {"name": "piercing_pattern", "label": "刺透形态", "description": "阳线深入前一根阴线实体50%以上"},
                    {"name": "inverted_hammer", "label": "倒锤头", "description": "上影线长的看涨信号"}
                ],
                "bearish": [
                    {"name": "engulfing", "label": "看跌吞噬", "description": "阳线后出现完全包含前一根阳线的阴线"},
                    {"name": "shooting_star", "label": "射击之星", "description": "上影线长、实体小的看跌信号"},
                    {"name": "gravestone_doji", "label": "墓碑十字", "description": "只有上影线的十字星"},
                    {"name": "evening_star", "label": "暮星", "description": "三根K线组成的强烈看跌信号"},
                    {"name": "dark_cloud_cover", "label": "乌云压顶", "description": "阴线深入前一根阳线实体50%以上"},
                    {"name": "hanging_man", "label": "上吊线", "description": "下影线长的看跌信号"}
                ]
            },
            "持续形态": {
                "bullish": [
                    {"name": "three_white_soldiers", "label": "三个白兵", "description": "连续三根上涨阳线"}
                ],
                "bearish": [
                    {"name": "three_black_crows", "label": "三只乌鸦", "description": "连续三根下跌阴线"}
                ]
            },
            "中性形态": {
                "neutral": [
                    {"name": "doji", "label": "十字星", "description": "开盘价与收盘价相近"},
                    {"name": "harami", "label": "母子线", "description": "第二根K线实体完全包含在第一根内"},
                    {"name": "harami_cross", "label": "十字孕线", "description": "母子线形态中第二根为十字星"}
                ]
            }
        }

        # 互斥形态组
        mutually_exclusive_groups = [
            {
                "name": "三线形态",
                "patterns": ["three_white_soldiers", "three_black_crows"],
                "description": "三个白兵和三只乌鸦不会同时出现"
            },
            {
                "name": "星形形态",
                "patterns": ["morning_star", "evening_star"],
                "description": "晨星和暮星是相对的形态"
            },
            {
                "name": "十字星形态",
                "patterns": ["dragonfly_doji", "gravestone_doji", "doji"],
                "description": "不同类型的十字星形态"
            }
        ]

        return JSONResponse(content={
            "categories": pattern_categories,
            "mutually_exclusive_groups": mutually_exclusive_groups,
            "total_patterns": sum(len(signals.get("bullish", [])) + len(signals.get("bearish", [])) + len(signals.get("neutral", []))
                                for signals in pattern_categories.values())
        })

    except Exception as e:
        logger.error(f"获取形态类型失败: {e}")
        raise HTTPException(status_code=500, detail="获取形态类型失败")

@router.get("/patterns/screening")
async def screen_patterns(
    pattern_types: Optional[str] = Query(None, description="形态类型，逗号分隔"),
    signal_type: Optional[str] = Query(None, description="信号类型: BULLISH, BEARISH, NEUTRAL"),
    min_confidence: float = Query(0.6, ge=0.0, le=1.0, description="最小置信度"),
    limit: int = Query(50, ge=1, le=200, description="返回数量")
):
    """形态筛选"""
    try:
        # 使用高性能数据服务批量加载
        from app.strategy.services.high_performance_data_service import HighPerformanceDataService
        hp_data_service = HighPerformanceDataService()

        # 获取最新日期
        available_dates = hp_data_service.get_available_dates()
        if not available_dates:
            return JSONResponse(content={
                "results": [],
                "total_analyzed": 0,
                "criteria": {
                    "pattern_types": pattern_types,
                    "signal_type": signal_type,
                    "min_confidence": min_confidence
                }
            })

        latest_date = available_dates[-1]

        # 获取股票列表
        symbols_list = hp_data_service.get_stock_list_for_date(latest_date)
        if not symbols_list:
            return JSONResponse(content={
                "results": [],
                "total_analyzed": 0,
                "criteria": {
                    "pattern_types": pattern_types,
                    "signal_type": signal_type,
                    "min_confidence": min_confidence
                }
            })

        # 解析筛选条件
        target_patterns = None
        if pattern_types:
            target_patterns = [p.strip() for p in pattern_types.split(',')]

        # 限制分析的股票数量以提高性能
        symbols_to_analyze = symbols_list[:min(len(symbols_list), limit)]

        # 批量加载历史数据（最近30天）
        start_date = available_dates[max(0, len(available_dates) - 30)]

        logger.info(f"批量加载 {len(symbols_to_analyze)} 只股票的历史数据: {start_date} 到 {latest_date}")

        # 批量加载数据
        batch_data = hp_data_service.load_stock_history_batch(
            symbols=symbols_to_analyze,
            end_date=latest_date,
            days=30
        )

        if not batch_data:
            return JSONResponse(content={
                "results": [],
                "total_analyzed": 0,
                "criteria": {
                    "pattern_types": pattern_types,
                    "signal_type": signal_type,
                    "min_confidence": min_confidence
                }
            })

        results = []
        analyzed_count = len(batch_data)

        logger.info(f"开始分析 {analyzed_count} 只股票的形态")

        for symbol, data in batch_data.items():
            try:
                if data.empty or len(data) < 3:  # 至少需要3天数据
                    continue

                # 检测形态
                patterns = pattern_engine.detect_all_patterns(data)

                # 筛选符合条件的形态
                matched_patterns = []
                for pattern_name, detections in patterns.items():
                    # 过滤形态类型
                    if target_patterns and pattern_name not in target_patterns:
                        continue

                    for detection in detections:
                        # 过滤信号类型
                        if signal_type and detection['signal'] != signal_type:
                            continue

                        # 过滤置信度
                        if detection['confidence'] < min_confidence:
                            continue

                        # 安全处理日期
                        if hasattr(detection['date'], 'strftime'):
                            date_str = detection['date'].strftime('%Y-%m-%d')
                        else:
                            try:
                                if isinstance(detection['date'], (int, float)) and detection['date'] < len(data):
                                    row_date = data.iloc[int(detection['date'])]['date']
                                    if hasattr(row_date, 'strftime'):
                                        date_str = row_date.strftime('%Y-%m-%d')
                                    else:
                                        date_str = str(row_date)
                                else:
                                    date_str = str(detection['date'])
                            except:
                                date_str = str(detection['date'])

                        matched_patterns.append({
                            'pattern_name': pattern_name,
                            'pattern_type': detection['type'],
                            'signal': detection['signal'],
                            'confidence': safe_float(detection['confidence']),
                            'date': date_str,
                            'description': detection['description']
                        })

                # 如果有匹配的形态，添加到结果中
                if matched_patterns:
                    # 获取最新价格信息
                    latest_data = data.iloc[-1] if not data.empty else None

                    # 安全获取价格和涨跌幅
                    latest_price = safe_float(latest_data['close']) if latest_data is not None else 0.0
                    change_percent = safe_float(latest_data.get('pct_change', 0)) if latest_data is not None else 0.0

                    results.append({
                        'symbol': symbol,
                        'name': symbol,  # 简化处理，直接使用symbol
                        'latest_price': latest_price,
                        'change_percent': change_percent,
                        'patterns': matched_patterns,
                        'pattern_count': len(matched_patterns),
                        'latest_pattern_date': max(p['date'] for p in matched_patterns) if matched_patterns else None
                    })

            except Exception as e:
                logger.warning(f"分析股票 {symbol} 形态失败: {e}")
                continue

        # 按形态数量排序
        results.sort(key=lambda x: x['pattern_count'], reverse=True)

        # 限制返回数量
        results = results[:limit]

        # 安全处理返回数据
        response_data = {
            "results": results,
            "total_analyzed": analyzed_count,
            "total_matched": len(results),
            "criteria": {
                "pattern_types": pattern_types,
                "signal_type": signal_type,
                "min_confidence": min_confidence,
                "limit": limit
            },
            "analysis_time": datetime.now().isoformat()
        }

        # 使用安全字典处理确保JSON序列化
        safe_response_data = safe_dict_values(response_data)

        return JSONResponse(content=safe_response_data)

    except Exception as e:
        logger.error(f"形态筛选失败: {e}")
        raise HTTPException(status_code=500, detail="形态筛选失败")

@router.get("/stats/performance")
async def get_performance_stats():
    """获取系统性能统计"""
    try:
        # 获取缓存统计
        cache_info = {
            "data_cache_size": data_service.load_daily_data.cache_info().currsize,
            "data_cache_hits": data_service.load_daily_data.cache_info().hits,
            "data_cache_misses": data_service.load_daily_data.cache_info().misses,
            "indicator_cache_size": indicator_service.calculate_indicators_cached.cache_info().currsize,
            "indicator_cache_hits": indicator_service.calculate_indicators_cached.cache_info().hits,
            "indicator_cache_misses": indicator_service.calculate_indicators_cached.cache_info().misses
        }

        return JSONResponse(content={
            "cache_stats": cache_info,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"获取性能统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取性能统计失败")
