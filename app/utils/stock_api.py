import akshare as ak
from typing import Dict, Optional, Tuple, List, Any
from app.models import StockInfo
import time
import pandas as pd
import datetime
import logging
import os
import json
import pickle

# 配置日志
#logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
# )
# logger = logging.getLogger(__name__)

from app.utils.logger import get_logger, set_log_config, set_log_level

# 获取当前模块的logger
logger = get_logger(__name__)


class StockAPI:
    """股票数据API，使用akshare获取股票数据"""
    
    # 缓存变量
    _stock_data_cache = None
    _last_cache_time = 0
    _cache_duration = 180  # 缓存时长，单位：秒
    
    # 文件缓存配置
    _cache_dir = "cache"  # 缓存目录
    _stock_data_file = "stock_data.pkl"  # 股票数据文件名
    _cache_info_file = "cache_info.json"  # 缓存信息文件名
    
    # 股票代码名称映射
    _stock_code_name_map = {
        "A": {},  # A股
        "US": {}, # 美股
        "HK": {}  # 港股
    }
    _stock_map_loaded = False
    
    @classmethod
    def _load_stock_map(cls):
        """加载股票代码和名称的映射关系"""
        if not cls._stock_map_loaded:
            try:
                logger.info("开始加载股票代码名称映射...")
                # 加载A股代码和名称
                stock_info_df = ak.stock_info_a_code_name()
                for _, row in stock_info_df.iterrows():
                    cls._stock_code_name_map["A"][row["code"]] = row["name"].strip()
                
                # 未来可以在这里添加美股和港股的加载逻辑
                # cls._stock_code_name_map["US"] = ...
                # cls._stock_code_name_map["HK"] = ...
                
                cls._stock_map_loaded = True
                logger.info(f"股票代码名称映射加载完成，共 {len(cls._stock_code_name_map['A'])} 只A股")
            except Exception as e:
                logger.error(f"加载股票代码名称映射出错: {e}", exc_info=True)
    
    @classmethod
    def _ensure_cache_dir(cls):
        """确保缓存目录存在"""
        if not os.path.exists(cls._cache_dir):
            os.makedirs(cls._cache_dir)
            logger.info(f"创建缓存目录: {cls._cache_dir}")
    
    @classmethod
    def _get_cache_file_path(cls, filename: str) -> str:
        """获取缓存文件的完整路径"""
        cls._ensure_cache_dir()
        return os.path.join(cls._cache_dir, filename)
    
    @classmethod
    def _save_stock_data_to_file(cls, data: pd.DataFrame, timestamp: float):
        """保存股票数据到文件"""
        try:
            file_path = cls._get_cache_file_path(cls._stock_data_file)
            info_path = cls._get_cache_file_path(cls._cache_info_file)
            
            # 保存数据
            with open(file_path, 'wb') as f:
                pickle.dump(data, f)
            
            # 保存缓存信息
            dt_obj = datetime.datetime.fromtimestamp(timestamp)
            cache_info = {
                'timestamp': timestamp,
                'datetime': dt_obj.strftime('%Y-%m-%d %H:%M:%S'),
                'data_shape': data.shape,
                'stock_count': len(data)
            }
            with open(info_path, 'w', encoding='utf-8') as f:
                json.dump(cache_info, f, ensure_ascii=False, indent=2)
            
            logger.info(f"股票数据已保存到文件: {file_path}, 股票数量: {len(data)}")
            return True
        except Exception as e:
            logger.error(f"保存股票数据到文件失败: {e}", exc_info=True)
            return False
    
    @classmethod
    def _load_stock_data_from_file(cls) -> Tuple[Optional[pd.DataFrame], Optional[float]]:
        """从文件加载股票数据"""
        try:
            file_path = cls._get_cache_file_path(cls._stock_data_file)
            info_path = cls._get_cache_file_path(cls._cache_info_file)
            
            if not os.path.exists(file_path) or not os.path.exists(info_path):
                logger.debug("缓存文件不存在")
                return None, None
            
            # 读取缓存信息
            with open(info_path, 'r', encoding='utf-8') as f:
                cache_info = json.load(f)
            
            # 读取数据
            with open(file_path, 'rb') as f:
                data = pickle.load(f)
            
            timestamp = cache_info['timestamp']
            logger.info(f"从文件加载股票数据成功: {cache_info['datetime']}, 股票数量: {len(data)}")
            return data, timestamp
            
        except Exception as e:
            logger.error(f"从文件加载股票数据失败: {e}", exc_info=True)
            return None, None
    
    @classmethod
    def _get_last_trading_day(cls) -> datetime.date:
        """获取最近的交易日"""
        today = datetime.date.today()
        current_weekday = today.weekday()
        
        # 如果是周末，返回周五
        if current_weekday >= 5:  # 周六(5)或周日(6)
            days_to_subtract = current_weekday - 4  # 周六减1天，周日减2天
            return today - datetime.timedelta(days=days_to_subtract)
        
        # 如果是工作日，返回今天
        return today
    
    @classmethod
    def _is_cache_from_today(cls, cache_timestamp: float) -> bool:
        """判断缓存是否来自今天"""
        cache_date = datetime.datetime.fromtimestamp(cache_timestamp).date()
        today = datetime.date.today()
        return cache_date == today
    
    @classmethod
    def _cleanup_old_cache(cls, max_days: int = 7):
        """清理超过指定天数的旧缓存文件"""
        try:
            file_path = cls._get_cache_file_path(cls._stock_data_file)
            info_path = cls._get_cache_file_path(cls._cache_info_file)
            
            if not os.path.exists(file_path) or not os.path.exists(info_path):
                return
            
            # 读取缓存信息
            with open(info_path, 'r', encoding='utf-8') as f:
                cache_info = json.load(f)
            
            cache_timestamp = cache_info['timestamp']
            cache_date = datetime.datetime.fromtimestamp(cache_timestamp).date()
            today = datetime.date.today()
            
            # 计算缓存天数
            days_old = (today - cache_date).days
            
            if days_old > max_days:
                logger.info(f"清理 {days_old} 天前的旧缓存文件")
                os.remove(file_path)
                os.remove(info_path)
                logger.info("旧缓存文件已清理")
                
        except Exception as e:
            logger.error(f"清理旧缓存文件失败: {e}", exc_info=True)
    
    @classmethod
    def _should_use_file_cache(cls, is_open: bool, file_timestamp: float) -> bool:
        """判断是否应该使用文件缓存"""
        if is_open:
            # 开盘时间：只使用今天的缓存，且不超过3小时
            if not cls._is_cache_from_today(file_timestamp):
                return False
            
            cache_age_hours = (time.time() - file_timestamp) / 3600
            return cache_age_hours <= 3
        else:
            # 非开盘时间：使用最近的缓存，但不超过7天
            cache_date = datetime.datetime.fromtimestamp(file_timestamp).date()
            today = datetime.date.today()
            days_old = (today - cache_date).days
            return days_old <= 7
    
    @classmethod
    def get_stock_name_from_map(cls, code: str, market: str = "A") -> Optional[str]:
        """从映射中获取股票名称
        
        Args:
            code: 股票代码
            market: 市场类型，可选值："A"(A股)、"US"(美股)、"HK"(港股)
            
        Returns:
            股票名称，如果未找到则返回None
        """
        if not cls._stock_map_loaded:
            cls._load_stock_map()
            
        name = cls._stock_code_name_map.get(market, {}).get(code)
        if name:
            logger.debug(f"从映射中找到股票 {code} 的名称: {name}")
        else:
            logger.debug(f"在映射中未找到股票 {code} 的名称")
        return name
    
    @staticmethod
    def _is_market_open() -> bool:
        """判断当前是否为A股开盘时间（工作日9:00-16:00）"""
        now = datetime.datetime.now()
        # 周一到周五
        if now.weekday() >= 5:
            return False
        # 9:00-16:00
        if 9 <= now.hour < 16:
            return True
        return False
    
    @classmethod
    def _get_stock_data(cls):
        """获取股票实时数据，带缓存功能"""
        current_time = time.time()
        # 判断当前是否为开盘时间
        is_open = cls._is_market_open()
        logger.debug(f"当前市场状态: {'开盘' if is_open else '休市'}")
        
        # 开盘时缓存3分钟，非开盘时缓存无限长（只要有缓存就不更新）
        cache_duration = 180 if is_open else float('inf')
        
        # 检查内存缓存是否需要更新
        cache_age = current_time - cls._last_cache_time
        need_refresh = (cls._stock_data_cache is None or cache_age > cache_duration)
        
        logger.debug(f"缓存状态检查: 缓存存在={cls._stock_data_cache is not None}, "
                    f"缓存时长={cache_age:.1f}秒, 需要刷新={need_refresh}")
        
        # 如果内存缓存不存在或已过期，则重新获取数据
        if need_refresh:
            # 如果是非开盘时间，先尝试从文件加载缓存
            if not is_open:
                logger.info("非开盘时间，尝试从文件加载缓存数据...")
                file_data, file_timestamp = cls._load_stock_data_from_file()
                
                if file_data is not None and not file_data.empty:
                    # 检查是否应该使用文件缓存
                    if cls._should_use_file_cache(is_open, file_timestamp):
                        logger.info("找到合适的文件缓存，使用文件数据")
                        cls._stock_data_cache = file_data
                        cls._last_cache_time = file_timestamp
                        return cls._stock_data_cache
                    else:
                        logger.info("文件缓存不合适，需要获取最新数据")
            
            # 获取最新数据
            try:
                logger.info("开始获取股票实时数据...")
                start_time = time.time()
                cls._stock_data_cache = ak.stock_zh_a_spot_em()
                end_time = time.time()
                cls._last_cache_time = current_time
                
                if cls._stock_data_cache is not None and not cls._stock_data_cache.empty:
                    logger.info(f"股票数据获取成功，耗时: {end_time - start_time:.2f}秒, "
                              f"股票数量: {len(cls._stock_data_cache)}, "
                              f"更新时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(current_time))}")
                    logger.debug(f"股票数据列: {list(cls._stock_data_cache.columns)}")
                    
                    # 保存到文件缓存
                    cls._save_stock_data_to_file(cls._stock_data_cache, current_time)
                    
                    # 清理旧缓存文件
                    cls._cleanup_old_cache()
                else:
                    logger.warning("获取到的股票数据为空")
                    
            except Exception as e:
                logger.error(f"获取股票数据出错: {e}", exc_info=True)
                
                # 如果获取失败，尝试从文件加载缓存
                if cls._stock_data_cache is None:
                    logger.info("获取数据失败，尝试从文件加载缓存...")
                    file_data, file_timestamp = cls._load_stock_data_from_file()
                    
                    if file_data is not None and not file_data.empty:
                        logger.info("使用文件缓存数据")
                        cls._stock_data_cache = file_data
                        cls._last_cache_time = file_timestamp
                    else:
                        logger.error("没有可用的股票数据缓存，返回空DataFrame")
                        return pd.DataFrame()
                else:
                    logger.warning("获取最新数据失败，继续使用内存缓存")
        else:
            logger.debug(f"使用内存缓存数据，缓存时长: {cache_age:.1f}秒")
            
        return cls._stock_data_cache
    
    @classmethod
    def get_stock_info(cls, code: str) -> Optional[StockInfo]:
        """获取股票实时信息"""
        logger.debug(f"开始获取股票 {code} 的信息")
        try:
            # 使用缓存获取股票数据
            stock_df = cls._get_stock_data()
            
            if stock_df is None or stock_df.empty:
                logger.warning(f"股票数据为空，无法获取 {code} 的信息")
                # 尝试从映射中获取基本信息
                name = cls.get_stock_name_from_map(code)
                if name:
                    logger.info(f"从映射中获取股票 {code} 基本信息: {name}")
                    return StockInfo(
                        code=code,
                        name=name,
                        price=0.0,
                        change_percent=0.0,
                        industry=None
                    )
                logger.warning(f"无法获取股票 {code} 的任何信息")
                return None
            
            # 查找指定股票
            stock_row = stock_df[stock_df['代码'] == code]
            
            if not stock_row.empty:
                logger.debug(f"在实时数据中找到股票 {code}")
                try:
                    stock_info = StockInfo(
                        code=code,
                        name=stock_row['名称'].values[0],
                        price=float(stock_row['最新价'].values[0]),
                        change_percent=float(stock_row['涨跌幅'].values[0]),
                        industry=stock_row['所属行业'].values[0] if '所属行业' in stock_row.columns else None
                    )
                    logger.debug(f"成功获取股票 {code} 信息: {stock_info.name}, 价格: {stock_info.price}")
                    return stock_info
                except (ValueError, IndexError) as e:
                    logger.error(f"解析股票 {code} 数据出错: {e}")
                    # 继续尝试从映射获取基本信息
            else:
                logger.debug(f"在实时数据中未找到股票 {code}")
            
            # 如果从实时数据获取失败，尝试从映射中获取基本信息
            name = cls.get_stock_name_from_map(code)
            if name:
                logger.info(f"从映射中获取股票 {code} 基本信息: {name}")
                return StockInfo(
                    code=code,
                    name=name,
                    price=0.0,
                    change_percent=0.0,
                    industry=None
                )
            
            logger.warning(f"无法获取股票 {code} 的任何信息")
            return None
            
        except Exception as e:
            logger.error(f"获取股票 {code} 信息出错: {e}", exc_info=True)
            
            # 出错时，尝试从映射中获取基本信息
            name = cls.get_stock_name_from_map(code)
            if name:
                logger.info(f"异常处理: 从映射中获取股票 {code} 基本信息: {name}")
                return StockInfo(
                    code=code,
                    name=name,
                    price=0.0,
                    change_percent=0.0,
                    industry=None
                )
                
            return None
    
    @classmethod
    def get_stock_name(cls, code: str) -> str:
        """获取股票名称"""
        logger.debug(f"获取股票 {code} 的名称")
        try:
            # 首先尝试从映射获取名称
            name = cls.get_stock_name_from_map(code)
            if name:
                return name
                
            # 如果映射中没有，再尝试从实时数据获取
            stock_df = cls._get_stock_data()
            
            if stock_df is not None and not stock_df.empty:
                stock_row = stock_df[stock_df['代码'] == code]
                
                if not stock_row.empty:
                    name = stock_row['名称'].values[0]
                    logger.debug(f"从实时数据中获取股票 {code} 名称: {name}")
                    return name
                    
            logger.debug(f"无法获取股票 {code} 名称，返回代码本身")
            return code
            
        except Exception as e:
            logger.error(f"获取股票 {code} 名称出错: {e}")
            
            # 出错时再次检查映射
            name = cls.get_stock_name_from_map(code)
            return name if name else code
    
    @staticmethod
    def get_eastmoney_url(code: str) -> str:
        """获取东方财富股票页面URL"""
        # 判断是沪市还是深市
        if code.startswith('6'):
            market = 1
        else:
            market = 0
        
        return f"https://stockpage.10jqka.com.cn/{code}" # 同花顺
        # return f"http://quote.eastmoney.com/sh{code}.html" if market == 1 else f"http://quote.eastmoney.com/sz{code}.html"

    @classmethod
    def clear_cache(cls):
        """清理所有缓存（内存和文件）"""
        try:
            # 清理内存缓存
            cls._stock_data_cache = None
            cls._last_cache_time = 0
            
            # 清理文件缓存
            file_path = cls._get_cache_file_path(cls._stock_data_file)
            info_path = cls._get_cache_file_path(cls._cache_info_file)
            
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info("已删除股票数据缓存文件")
            
            if os.path.exists(info_path):
                os.remove(info_path)
                logger.info("已删除缓存信息文件")
                
            logger.info("所有缓存已清理")
            
        except Exception as e:
            logger.error(f"清理缓存失败: {e}", exc_info=True)
    
    @classmethod
    def get_cache_status(cls) -> Dict[str, Any]:
        """获取缓存状态信息"""
        status = {
            'memory_cache': {
                'exists': cls._stock_data_cache is not None,
                'last_update': cls._last_cache_time,
                'last_update_str': datetime.datetime.fromtimestamp(cls._last_cache_time).strftime('%Y-%m-%d %H:%M:%S') if cls._last_cache_time > 0 else None,
                'stock_count': len(cls._stock_data_cache) if cls._stock_data_cache is not None else 0
            },
            'file_cache': {
                'exists': False,
                'last_update': None,
                'last_update_str': None,
                'stock_count': 0
            },
            'market_status': {
                'is_open': cls._is_market_open(),
                'current_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        }
        
        # 检查文件缓存
        try:
            file_data, file_timestamp = cls._load_stock_data_from_file()
            if file_data is not None:
                status['file_cache'] = {
                    'exists': True,
                    'last_update': file_timestamp,
                    'last_update_str': datetime.datetime.fromtimestamp(file_timestamp).strftime('%Y-%m-%d %H:%M:%S'),
                    'stock_count': len(file_data)
                }
        except Exception as e:
            logger.error(f"获取文件缓存状态失败: {e}")
        
        return status

if __name__ == '__main__':
    stock = StockAPI()
    stock.get_stock_info('002130')