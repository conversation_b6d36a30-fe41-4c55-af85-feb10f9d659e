<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>K线形态筛选 - 量化分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .filter-card {
            border-left: 4px solid #0d6efd;
            background: #f8f9fa;
        }
        .result-card {
            transition: transform 0.2s;
            cursor: pointer;
        }
        .result-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .pattern-badge {
            font-size: 0.75rem;
            margin: 0.125rem;
        }
        .price-up { color: #dc3545; }
        .price-down { color: #198754; }
        .price-neutral { color: #6c757d; }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        .pattern-signal-bullish { background-color: #d1e7dd; color: #0f5132; }
        .pattern-signal-bearish { background-color: #f8d7da; color: #842029; }
        .pattern-signal-neutral { background-color: #e2e3e5; color: #41464b; }

        /* 多选下拉框样式 */
        #patternTypes {
            min-height: 120px;
        }
        #patternTypes optgroup {
            font-weight: bold;
            color: #495057;
        }
        #patternTypes option {
            padding: 4px 8px;
            font-weight: normal;
        }
        #patternTypes option[data-signal="BULLISH"] {
            background-color: #f8f9fa;
            color: #198754;
        }
        #patternTypes option[data-signal="BEARISH"] {
            background-color: #f8f9fa;
            color: #dc3545;
        }
        #patternTypes option[data-signal="NEUTRAL"] {
            background-color: #f8f9fa;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-graph-up"></i> 量化分析系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/quantitative/stocks">股票列表</a>
                <a class="nav-link" href="/quantitative/indicators">指标排行</a>
                <a class="nav-link" href="/quantitative/market">市场概览</a>
                <a class="nav-link active" href="/quantitative/patterns">形态筛选</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="bi bi-funnel"></i> K线形态筛选</h2>
                <p class="text-muted">根据K线形态特征筛选股票，发现投资机会</p>
            </div>
        </div>

        <!-- 筛选条件 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card filter-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-sliders"></i> 筛选条件</h5>
                    </div>
                    <div class="card-body">
                        <form id="screeningForm">
                            <div class="row">
                                <div class="col-md-2">
                                    <label for="signalType" class="form-label">信号类型</label>
                                    <select class="form-select" id="signalType" name="signal_type">
                                        <option value="">全部</option>
                                        <option value="BULLISH" selected>看涨</option>
                                        <option value="BEARISH">看跌</option>
                                        <option value="NEUTRAL">中性</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="minConfidence" class="form-label">最小置信度</label>
                                    <select class="form-select" id="minConfidence" name="min_confidence">
                                        <option value="0.5">50%</option>
                                        <option value="0.6" selected>60%</option>
                                        <option value="0.7">70%</option>
                                        <option value="0.8">80%</option>
                                        <option value="0.9">90%</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">形态类型</label>
                                    <select class="form-select" id="patternTypes" name="pattern_types" multiple size="6">
                                        <optgroup label="反转形态 - 看涨">
                                            <option value="engulfing" data-signal="BULLISH">看涨吞噬</option>
                                            <option value="hammer" data-signal="BULLISH">锤头线</option>
                                            <option value="dragonfly_doji" data-signal="BULLISH">蜻蜓十字</option>
                                            <option value="morning_star" data-signal="BULLISH">晨星</option>
                                            <option value="piercing_pattern" data-signal="BULLISH">刺透形态</option>
                                            <option value="inverted_hammer" data-signal="BULLISH">倒锤头</option>
                                        </optgroup>
                                        <optgroup label="反转形态 - 看跌">
                                            <option value="engulfing" data-signal="BEARISH">看跌吞噬</option>
                                            <option value="shooting_star" data-signal="BEARISH">射击之星</option>
                                            <option value="gravestone_doji" data-signal="BEARISH">墓碑十字</option>
                                            <option value="evening_star" data-signal="BEARISH">暮星</option>
                                            <option value="dark_cloud_cover" data-signal="BEARISH">乌云压顶</option>
                                            <option value="hanging_man" data-signal="BEARISH">上吊线</option>
                                        </optgroup>
                                        <optgroup label="持续形态">
                                            <option value="three_white_soldiers" data-signal="BULLISH">三个白兵</option>
                                            <option value="three_black_crows" data-signal="BEARISH">三只乌鸦</option>
                                        </optgroup>
                                        <optgroup label="中性形态">
                                            <option value="doji" data-signal="NEUTRAL">十字星</option>
                                            <option value="harami" data-signal="NEUTRAL">母子线</option>
                                            <option value="harami_cross" data-signal="NEUTRAL">十字孕线</option>
                                        </optgroup>
                                    </select>
                                    <div class="form-text mt-2">
                                        <small>按住Ctrl键可多选，不选择则包含所有形态类型</small>
                                        <br><small class="text-info">💡 提示：三个白兵与三只乌鸦互斥，晨星与暮星互斥</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <label for="limit" class="form-label">返回数量</label>
                                    <select class="form-select" id="limit" name="limit">
                                        <option value="20">20</option>
                                        <option value="50" selected>50</option>
                                        <option value="100">100</option>
                                        <option value="200">200</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-search"></i> 开始筛选
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetForm()">
                                        <i class="bi bi-arrow-clockwise"></i> 重置
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选结果 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-list-ul"></i> 筛选结果</h5>
                        <div id="resultStats" class="text-muted"></div>
                    </div>
                    <div class="card-body">
                        <div id="screeningResults">
                            <div class="text-center text-muted py-5">
                                <i class="bi bi-search display-4"></i>
                                <p class="mt-3">请设置筛选条件并点击"开始筛选"</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="text-center">
            <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                <span class="visually-hidden">筛选中...</span>
            </div>
            <p class="mt-3 text-primary">正在筛选股票形态，请稍候...</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
        });

        // 设置事件监听器
        function setupEventListeners() {
            const form = document.getElementById('screeningForm');
            form.addEventListener('submit', handleScreening);
        }

        // 处理筛选表单提交
        async function handleScreening(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const params = new URLSearchParams();
            
            // 构建查询参数
            if (formData.get('signal_type')) {
                params.append('signal_type', formData.get('signal_type'));
            }
            
            if (formData.get('min_confidence')) {
                params.append('min_confidence', formData.get('min_confidence'));
            }
            
            if (formData.get('limit')) {
                params.append('limit', formData.get('limit'));
            }
            
            // 处理多选下拉框形式的形态类型
            const patternSelect = document.getElementById('patternTypes');
            const selectedPatterns = Array.from(patternSelect.selectedOptions).map(option => option.value);
            if (selectedPatterns.length > 0) {
                params.append('pattern_types', selectedPatterns.join(','));
            }
            
            showLoading(true);
            
            try {
                const response = await fetch(`/api/quantitative/patterns/screening?${params.toString()}`);
                
                if (!response.ok) {
                    throw new Error('筛选请求失败');
                }
                
                const data = await response.json();
                renderResults(data);
                
            } catch (error) {
                console.error('筛选失败:', error);
                showError('筛选失败，请稍后重试');
            } finally {
                showLoading(false);
            }
        }

        // 渲染筛选结果
        function renderResults(data) {
            const container = document.getElementById('screeningResults');
            const statsContainer = document.getElementById('resultStats');
            
            // 更新统计信息
            statsContainer.textContent = `分析了 ${data.total_analyzed} 只股票，找到 ${data.total_matched} 只符合条件的股票`;
            
            if (!data.results || data.results.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-inbox display-4"></i>
                        <p class="mt-3">未找到符合条件的股票</p>
                        <p class="text-sm">请尝试调整筛选条件</p>
                    </div>
                `;
                return;
            }
            
            let html = '<div class="row">';
            
            data.results.forEach(stock => {
                const priceChangeClass = stock.change_percent > 0 ? 'price-up' :
                                       stock.change_percent < 0 ? 'price-down' : 'price-neutral';
                
                const priceChangeIcon = stock.change_percent > 0 ? '↗' :
                                      stock.change_percent < 0 ? '↘' : '→';
                
                // 生成形态标签
                let patternBadges = '';
                stock.patterns.forEach(pattern => {
                    const badgeClass = pattern.signal === 'BULLISH' ? 'pattern-signal-bullish' :
                                     pattern.signal === 'BEARISH' ? 'pattern-signal-bearish' : 'pattern-signal-neutral';
                    
                    patternBadges += `
                        <span class="badge ${badgeClass} pattern-badge" title="${pattern.description}">
                            ${pattern.description} (${(pattern.confidence * 100).toFixed(0)}%)
                        </span>
                    `;
                });
                
                html += `
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card result-card h-100" onclick="viewStockDetail('${stock.symbol}')">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="card-title mb-1">${stock.symbol}</h6>
                                        <p class="card-text text-muted small mb-0">${stock.name}</p>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold ${priceChangeClass}">¥${stock.latest_price.toFixed(2)}</div>
                                        <small class="${priceChangeClass}">
                                            ${priceChangeIcon} ${stock.change_percent.toFixed(2)}%
                                        </small>
                                    </div>
                                </div>
                                
                                <div class="mb-2">
                                    <small class="text-muted">检测到 ${stock.pattern_count} 个形态</small>
                                </div>
                                
                                <div class="pattern-badges">
                                    ${patternBadges}
                                </div>
                                
                                <div class="mt-2">
                                    <small class="text-muted">
                                        最新形态: ${stock.latest_pattern_date || '无'}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            container.innerHTML = html;
        }

        // 查看股票详情
        function viewStockDetail(symbol) {
            window.open(`/quantitative/stocks/${symbol}`, '_blank');
        }

        // 重置表单
        function resetForm() {
            document.getElementById('screeningForm').reset();
            document.getElementById('signalType').value = 'BULLISH';
            document.getElementById('minConfidence').value = '0.6';
            document.getElementById('limit').value = '50';

            // 取消所有形态类型的选择
            document.querySelectorAll('#patternTypesGroup input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = false;
            });

            // 清空结果
            document.getElementById('screeningResults').innerHTML = `
                <div class="text-center text-muted py-5">
                    <i class="bi bi-search display-4"></i>
                    <p class="mt-3">请设置筛选条件并点击"开始筛选"</p>
                </div>
            `;
            document.getElementById('resultStats').textContent = '';
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            const overlay = document.getElementById('loadingOverlay');
            overlay.style.display = show ? 'flex' : 'none';
        }

        // 显示错误信息
        function showError(message) {
            const container = document.getElementById('screeningResults');
            container.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    ${message}
                </div>
            `;
        }
    </script>
</body>
</html>
