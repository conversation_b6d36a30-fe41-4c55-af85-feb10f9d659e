"""
技术指标计算引擎

基于talib和pandas实现各种技术指标计算
"""
import logging
from typing import Dict, Any, Optional, List
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)

class IndicatorEngine:
    """技术指标计算引擎"""
    
    def __init__(self):
        self.supported_indicators = [
            'MACD', 'KDJ', 'BOLL', 'TRIX', 'TRMA', 'CR', 'SMA', 'RSI',
            'VR', 'MAVR', 'ROC', 'DMI', 'WR', 'CCI', 'TR', 'ATR',
            'DMA', 'AMA', 'OBV', 'SAR', 'PSY', 'BRAR', 'EMV', 'BIAS',
            'TEMA', 'MFI', 'VWMA', 'PPO', 'WT', 'SUPERTREND', 'DPO',
            'VHF', 'RVI', 'FI', 'ENE', 'STOCHRSI'
        ]
    
    def calculate_all_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算所有支持的技术指标

        Args:
            data: 股票数据，包含OHLCV列

        Returns:
            包含所有指标的DataFrame
        """
        if data.empty:
            logger.debug("输入数据为空，返回空DataFrame")
            return data

        # 检查数据量是否足够计算技术指标
        if len(data) < 14:
            logger.debug(f"数据量不足以计算技术指标: {len(data)} < 14，返回原始数据")
            return data.copy()

        result = data.copy()

        # 数据清洗和验证
        result = self._clean_and_validate_data(result)

        # 再次检查清洗后的数据量
        if len(result) < 14:
            logger.debug(f"数据清洗后数据量不足: {len(result)} < 14，返回清洗后的数据")
            return result

        # 基础指标
        result = self._add_basic_indicators(result)

        # 趋势指标
        result = self._add_trend_indicators(result)

        # 震荡指标
        result = self._add_oscillator_indicators(result)

        # 成交量指标
        result = self._add_volume_indicators(result)

        # 图形指标
        result = self._add_chart_indicators(result)

        # 额外指标
        result = self.calculate_additional_indicators(result)

        return result

    def _add_chart_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加图形指标"""
        try:
            # 支撑阻力位
            data = self._add_support_resistance(data)

            # 趋势线
            data = self._add_trend_lines(data)

            # 形态识别
            data = self._add_pattern_recognition(data)

            # 波浪理论
            data = self._add_wave_analysis(data)

            # 斐波那契回调
            data = self._add_fibonacci_retracement(data)

            return data

        except Exception as e:
            logger.error(f"添加图形指标失败: {e}")
            return data

    def _add_support_resistance(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加支撑阻力位"""
        try:
            if len(data) < 20:
                return data

            # 计算局部高低点
            high_series = data['high']
            low_series = data['low']

            # 阻力位（局部高点）
            resistance_levels = []
            support_levels = []

            window = 10
            for i in range(window, len(data) - window):
                # 检查是否为局部高点
                if (high_series.iloc[i] == high_series.iloc[i-window:i+window+1].max()):
                    resistance_levels.append((i, high_series.iloc[i]))

                # 检查是否为局部低点
                if (low_series.iloc[i] == low_series.iloc[i-window:i+window+1].min()):
                    support_levels.append((i, low_series.iloc[i]))

            # 添加支撑阻力位标记
            data['resistance_level'] = np.nan
            data['support_level'] = np.nan

            for idx, level in resistance_levels:
                data.iloc[idx, data.columns.get_loc('resistance_level')] = level

            for idx, level in support_levels:
                data.iloc[idx, data.columns.get_loc('support_level')] = level

            return data

        except Exception as e:
            logger.error(f"计算支撑阻力位失败: {e}")
            return data

    def _add_trend_lines(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加趋势线"""
        try:
            if len(data) < 30:
                return data

            # 简单趋势线计算
            close_prices = data['close'].values

            # 短期趋势（20日）
            short_trend = np.polyfit(range(20), close_prices[-20:], 1)
            data['short_trend_slope'] = short_trend[0]
            data['short_trend_direction'] = np.where(short_trend[0] > 0, 'up', 'down')

            # 中期趋势（60日）
            if len(data) >= 60:
                mid_trend = np.polyfit(range(60), close_prices[-60:], 1)
                data['mid_trend_slope'] = mid_trend[0]
                data['mid_trend_direction'] = np.where(mid_trend[0] > 0, 'up', 'down')
            else:
                data['mid_trend_slope'] = np.nan
                data['mid_trend_direction'] = 'neutral'

            return data

        except Exception as e:
            logger.error(f"计算趋势线失败: {e}")
            return data

    def _add_pattern_recognition(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加形态识别"""
        try:
            if len(data) < 10:
                return data

            # 简单的K线形态识别
            open_prices = data['open']
            high_prices = data['high']
            low_prices = data['low']
            close_prices = data['close']

            # 十字星
            body_size = abs(close_prices - open_prices)
            total_range = high_prices - low_prices
            doji = (body_size / total_range < 0.1) & (total_range > 0)

            # 锤子线
            lower_shadow = np.where(close_prices > open_prices,
                                  open_prices - low_prices,
                                  close_prices - low_prices)
            upper_shadow = np.where(close_prices > open_prices,
                                  high_prices - close_prices,
                                  high_prices - open_prices)
            hammer = (lower_shadow > 2 * body_size) & (upper_shadow < body_size)

            # 吞没形态
            bullish_engulfing = (
                (close_prices > open_prices) &  # 当前为阳线
                (close_prices.shift(1) < open_prices.shift(1)) &  # 前一根为阴线
                (open_prices < close_prices.shift(1)) &  # 当前开盘价低于前一根收盘价
                (close_prices > open_prices.shift(1))  # 当前收盘价高于前一根开盘价
            )

            bearish_engulfing = (
                (close_prices < open_prices) &  # 当前为阴线
                (close_prices.shift(1) > open_prices.shift(1)) &  # 前一根为阳线
                (open_prices > close_prices.shift(1)) &  # 当前开盘价高于前一根收盘价
                (close_prices < open_prices.shift(1))  # 当前收盘价低于前一根开盘价
            )

            # 添加形态标记
            data['pattern_doji'] = doji
            data['pattern_hammer'] = hammer
            data['pattern_bullish_engulfing'] = bullish_engulfing
            data['pattern_bearish_engulfing'] = bearish_engulfing

            return data

        except Exception as e:
            logger.error(f"形态识别失败: {e}")
            return data

    def _add_wave_analysis(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加波浪分析"""
        try:
            if len(data) < 50:
                return data

            # 简化的波浪分析
            close_prices = data['close']

            # 计算波峰波谷（如果scipy可用）
            try:
                from scipy.signal import find_peaks
            except ImportError:
                logger.warning("scipy未安装，跳过波浪分析")
                return data

            # 找波峰
            peaks, _ = find_peaks(close_prices, distance=5, prominence=close_prices.std()*0.5)

            # 找波谷
            valleys, _ = find_peaks(-close_prices, distance=5, prominence=close_prices.std()*0.5)

            # 标记波浪
            data['wave_peak'] = False
            data['wave_valley'] = False

            if len(peaks) > 0:
                data.iloc[peaks, data.columns.get_loc('wave_peak')] = True

            if len(valleys) > 0:
                data.iloc[valleys, data.columns.get_loc('wave_valley')] = True

            return data

        except Exception as e:
            logger.error(f"波浪分析失败: {e}")
            return data

    def _add_fibonacci_retracement(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加斐波那契回调"""
        try:
            if len(data) < 30:
                return data

            # 计算最近30天的高低点
            recent_data = data.tail(30)
            high_price = recent_data['high'].max()
            low_price = recent_data['low'].min()

            # 斐波那契回调位
            diff = high_price - low_price

            data['fib_0'] = high_price
            data['fib_236'] = high_price - diff * 0.236
            data['fib_382'] = high_price - diff * 0.382
            data['fib_500'] = high_price - diff * 0.500
            data['fib_618'] = high_price - diff * 0.618
            data['fib_100'] = low_price

            return data

        except Exception as e:
            logger.error(f"斐波那契回调计算失败: {e}")
            return data

    def _clean_and_validate_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """清洗和验证数据"""
        try:
            # 确保必要的列存在
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in data.columns]

            if missing_columns:
                logger.warning(f"缺少必要列: {missing_columns}")
                return data

            # 强制转换数据类型
            for col in required_columns:
                if col in data.columns:
                    # 处理可能的字符串数据
                    if data[col].dtype == 'object':
                        # 清理字符串格式
                        data[col] = (data[col].astype(str)
                                   .str.replace(',', '')
                                   .str.replace('%', '')
                                   .str.replace('--', '')
                                   .str.replace('-', '')
                                   .str.strip())
                        # 替换无效值
                        data[col] = data[col].replace(['', 'nan', 'None', 'null'], np.nan)

                    # 转换为数值类型
                    data[col] = pd.to_numeric(data[col], errors='coerce')

                    # 确保价格数据非负
                    if col in ['open', 'high', 'low', 'close', 'volume']:
                        data[col] = data[col].where(data[col] >= 0)

            # 移除无效行
            data = data.dropna(subset=['close'])

            # 确保价格逻辑正确
            data = data[
                (data['high'] >= data['low']) &
                (data['high'] >= data['close']) &
                (data['low'] <= data['close']) &
                (data['close'] > 0)
            ]

            return data

        except Exception as e:
            logger.error(f"数据清洗失败: {e}")
            return data

    def _add_basic_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加基础指标"""
        # SMA - 简单移动平均
        for period in [5, 10, 20, 60]:
            data[f'SMA_{period}'] = data['close'].rolling(window=period).mean()
        
        # EMA - 指数移动平均
        for period in [5, 10, 20, 60]:
            data[f'EMA_{period}'] = data['close'].ewm(span=period).mean()
        
        return data
    
    def _add_trend_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加趋势指标"""
        # MACD
        macd_data = self._calculate_macd(data)
        data = pd.concat([data, macd_data], axis=1)
        
        # BOLL - 布林带
        boll_data = self._calculate_boll(data)
        data = pd.concat([data, boll_data], axis=1)
        
        # TRIX
        data['TRIX'] = self._calculate_trix(data['close'])
        
        return data
    
    def _add_oscillator_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加震荡指标"""
        try:
            # RSI
            if len(data) >= 14:
                data['RSI'] = self._calculate_rsi(data['close'])
            else:
                data['RSI'] = np.nan
                logger.debug("数据不足，跳过RSI计算")

            # KDJ
            if len(data) >= 9:
                kdj_data = self._calculate_kdj(data)
                data = pd.concat([data, kdj_data], axis=1)
            else:
                data['K'] = np.nan
                data['D'] = np.nan
                data['J'] = np.nan
                logger.debug("数据不足，跳过KDJ计算")

            # CCI
            if len(data) >= 14:
                data['CCI'] = self._calculate_cci(data)
            else:
                data['CCI'] = np.nan
                logger.debug("数据不足，跳过CCI计算")

            # WR - 威廉指标
            if len(data) >= 14:
                data['WR'] = self._calculate_wr(data)
            else:
                data['WR'] = np.nan
                logger.debug("数据不足，跳过WR计算")

        except Exception as e:
            logger.error(f"计算震荡指标失败: {e}")

        return data
    
    def _add_volume_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加成交量指标"""
        # OBV - 能量潮
        data['OBV'] = self._calculate_obv(data)
        
        # VR - 成交量比率
        data['VR'] = self._calculate_vr(data)
        
        return data
    
    def _calculate_macd(self, data: pd.DataFrame, fast=12, slow=26, signal=9) -> pd.DataFrame:
        """计算MACD指标"""
        close = data['close']
        
        # 计算EMA
        ema_fast = close.ewm(span=fast).mean()
        ema_slow = close.ewm(span=slow).mean()
        
        # MACD线
        macd_line = ema_fast - ema_slow
        
        # 信号线
        signal_line = macd_line.ewm(span=signal).mean()
        
        # 柱状图
        histogram = macd_line - signal_line
        
        return pd.DataFrame({
            'MACD': macd_line,
            'MACD_Signal': signal_line,
            'MACD_Histogram': histogram
        })
    
    def _calculate_kdj(self, data: pd.DataFrame, k_period=9, d_period=3) -> pd.DataFrame:
        """计算KDJ指标"""
        high = data['high']
        low = data['low']
        close = data['close']
        
        # 计算RSV
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        rsv = (close - lowest_low) / (highest_high - lowest_low) * 100
        
        # 计算K值
        k = rsv.ewm(alpha=1/d_period).mean()
        
        # 计算D值
        d = k.ewm(alpha=1/d_period).mean()
        
        # 计算J值
        j = 3 * k - 2 * d
        
        return pd.DataFrame({
            'KDJ_K': k,
            'KDJ_D': d,
            'KDJ_J': j
        })
    
    def _calculate_boll(self, data: pd.DataFrame, period=20, std_dev=2) -> pd.DataFrame:
        """计算布林带"""
        close = data['close']
        
        # 中轨（移动平均）
        middle = close.rolling(window=period).mean()
        
        # 标准差
        std = close.rolling(window=period).std()
        
        # 上轨和下轨
        upper = middle + std_dev * std
        lower = middle - std_dev * std
        
        return pd.DataFrame({
            'BOLL_Upper': upper,
            'BOLL_Middle': middle,
            'BOLL_Lower': lower
        })
    
    def _calculate_rsi(self, close: pd.Series, period=14) -> pd.Series:
        """计算RSI指标"""
        try:
            # 确保输入是数值类型的Series
            if not isinstance(close, pd.Series):
                close = pd.Series(close)

            # 强制转换为数值类型
            close = pd.to_numeric(close, errors='coerce')

            # 移除NaN值
            close = close.dropna()

            if len(close) < period:
                logger.debug(f"RSI计算数据不足: {len(close)} < {period}，返回空序列")
                # 返回与原始索引匹配的空序列
                if hasattr(close, 'index') and len(close.index) > 0:
                    return pd.Series(index=close.index, dtype=float)
                else:
                    return pd.Series(dtype=float)

            delta = close.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

            # 避免除零错误
            rs = gain / loss.replace(0, np.nan)
            rsi = 100 - (100 / (1 + rs))

            return rsi

        except Exception as e:
            logger.error(f"RSI计算失败: {e}")
            # 返回与输入长度匹配的空序列
            if hasattr(close, 'index'):
                return pd.Series(index=close.index, dtype=float)
            else:
                return pd.Series(dtype=float)
    
    def _calculate_cci(self, data: pd.DataFrame, period=14) -> pd.Series:
        """计算CCI指标"""
        high = data['high']
        low = data['low']
        close = data['close']
        
        # 典型价格
        tp = (high + low + close) / 3
        
        # 移动平均
        ma = tp.rolling(window=period).mean()
        
        # 平均绝对偏差
        mad = tp.rolling(window=period).apply(lambda x: np.mean(np.abs(x - x.mean())))
        
        # CCI
        cci = (tp - ma) / (0.015 * mad)
        
        return cci
    
    def _calculate_wr(self, data: pd.DataFrame, period=14) -> pd.Series:
        """计算威廉指标"""
        high = data['high']
        low = data['low']
        close = data['close']
        
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        
        wr = (highest_high - close) / (highest_high - lowest_low) * 100
        
        return wr
    
    def _calculate_obv(self, data: pd.DataFrame) -> pd.Series:
        """计算OBV指标"""
        close = data['close']
        volume = data['volume']
        
        # 价格变化方向
        price_change = close.diff()
        
        # OBV计算
        obv = pd.Series(index=close.index, dtype=float)
        obv.iloc[0] = volume.iloc[0]
        
        for i in range(1, len(close)):
            if price_change.iloc[i] > 0:
                obv.iloc[i] = obv.iloc[i-1] + volume.iloc[i]
            elif price_change.iloc[i] < 0:
                obv.iloc[i] = obv.iloc[i-1] - volume.iloc[i]
            else:
                obv.iloc[i] = obv.iloc[i-1]
        
        return obv
    
    def _calculate_vr(self, data: pd.DataFrame, period=26) -> pd.Series:
        """计算VR指标"""
        close = data['close']
        volume = data['volume']
        
        # 价格变化
        price_change = close.diff()
        
        # 上涨日成交量
        up_volume = volume.where(price_change > 0, 0)
        
        # 下跌日成交量
        down_volume = volume.where(price_change < 0, 0)
        
        # 平盘日成交量
        equal_volume = volume.where(price_change == 0, 0)
        
        # VR计算
        up_sum = up_volume.rolling(window=period).sum()
        down_sum = down_volume.rolling(window=period).sum()
        equal_sum = equal_volume.rolling(window=period).sum()
        
        vr = (up_sum + equal_sum/2) / (down_sum + equal_sum/2) * 100
        
        return vr
    
    def _calculate_trix(self, close: pd.Series, period=14) -> pd.Series:
        """计算TRIX指标"""
        # 三重指数平滑
        ema1 = close.ewm(span=period).mean()
        ema2 = ema1.ewm(span=period).mean()
        ema3 = ema2.ewm(span=period).mean()

        # TRIX
        trix = ema3.pct_change() * 100

        return trix

    def calculate_additional_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算额外的技术指标"""
        result = data.copy()

        # CR指标
        result['CR'] = self._calculate_cr(data)

        # ROC指标
        result['ROC'] = self._calculate_roc(data['close'])

        # DMI指标系列
        dmi_data = self._calculate_dmi(data)
        result = pd.concat([result, dmi_data], axis=1)

        # ATR指标
        result['ATR'] = self._calculate_atr(data)

        # SAR指标
        result['SAR'] = self._calculate_sar(data)

        # BIAS指标
        for period in [6, 12, 24]:
            result[f'BIAS_{period}'] = self._calculate_bias(data['close'], period)

        # MFI指标
        result['MFI'] = self._calculate_mfi(data)

        return result

    def _calculate_cr(self, data: pd.DataFrame, period=26) -> pd.Series:
        """计算CR指标"""
        high = data['high']
        low = data['low']
        close = data['close']

        # 昨日中价
        mid_price = (high + low + close) / 3
        prev_mid = mid_price.shift(1)

        # 计算P1, P2, P3, P4
        p1 = (high - prev_mid).where(high > prev_mid, 0)
        p2 = (prev_mid - low).where(prev_mid > low, 0)
        p3 = (high - prev_mid).abs()
        p4 = (prev_mid - low).abs()

        # CR计算
        cr = p1.rolling(window=period).sum() / p2.rolling(window=period).sum() * 100

        return cr

    def _calculate_roc(self, close: pd.Series, period=12) -> pd.Series:
        """计算ROC指标"""
        roc = (close / close.shift(period) - 1) * 100
        return roc

    def _calculate_dmi(self, data: pd.DataFrame, period=14) -> pd.DataFrame:
        """计算DMI指标系列"""
        high = data['high']
        low = data['low']
        close = data['close']

        # 计算TR
        tr1 = high - low
        tr2 = (high - close.shift(1)).abs()
        tr3 = (low - close.shift(1)).abs()
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

        # 计算DM
        dm_plus = (high - high.shift(1)).where((high - high.shift(1)) > (low.shift(1) - low), 0)
        dm_minus = (low.shift(1) - low).where((low.shift(1) - low) > (high - high.shift(1)), 0)

        # 平滑处理
        tr_smooth = tr.rolling(window=period).mean()
        dm_plus_smooth = dm_plus.rolling(window=period).mean()
        dm_minus_smooth = dm_minus.rolling(window=period).mean()

        # 计算DI
        di_plus = dm_plus_smooth / tr_smooth * 100
        di_minus = dm_minus_smooth / tr_smooth * 100

        # 计算DX和ADX
        dx = (di_plus - di_minus).abs() / (di_plus + di_minus) * 100
        adx = dx.rolling(window=period).mean()

        # 计算ADXR
        adxr = (adx + adx.shift(period)) / 2

        return pd.DataFrame({
            'DI_Plus': di_plus,
            'DI_Minus': di_minus,
            'DX': dx,
            'ADX': adx,
            'ADXR': adxr
        })

    def _calculate_atr(self, data: pd.DataFrame, period=14) -> pd.Series:
        """计算ATR指标"""
        high = data['high']
        low = data['low']
        close = data['close']

        # 计算TR
        tr1 = high - low
        tr2 = (high - close.shift(1)).abs()
        tr3 = (low - close.shift(1)).abs()
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

        # ATR是TR的移动平均
        atr = tr.rolling(window=period).mean()

        return atr

    def _calculate_sar(self, data: pd.DataFrame, af_start=0.02, af_increment=0.02, af_max=0.2) -> pd.Series:
        """计算SAR指标（简化版本）"""
        high = data['high']
        low = data['low']

        # 简化的SAR计算
        # 实际SAR计算比较复杂，这里提供一个简化版本
        sar = pd.Series(index=data.index, dtype=float)

        if len(data) < 2:
            return sar

        # 初始值
        sar.iloc[0] = low.iloc[0]

        for i in range(1, len(data)):
            # 简化计算，实际应该考虑趋势转换
            if high.iloc[i] > high.iloc[i-1]:
                sar.iloc[i] = min(low.iloc[i-1], sar.iloc[i-1])
            else:
                sar.iloc[i] = max(high.iloc[i-1], sar.iloc[i-1])

        return sar

    def _calculate_bias(self, close: pd.Series, period=6) -> pd.Series:
        """计算BIAS指标"""
        ma = close.rolling(window=period).mean()
        bias = (close - ma) / ma * 100
        return bias

    def _calculate_mfi(self, data: pd.DataFrame, period=14) -> pd.Series:
        """计算MFI指标"""
        high = data['high']
        low = data['low']
        close = data['close']
        volume = data['volume']

        # 典型价格
        tp = (high + low + close) / 3

        # 资金流量
        mf = tp * volume

        # 正负资金流量
        positive_mf = mf.where(tp > tp.shift(1), 0)
        negative_mf = mf.where(tp < tp.shift(1), 0)

        # MFI计算
        positive_mf_sum = positive_mf.rolling(window=period).sum()
        negative_mf_sum = negative_mf.rolling(window=period).sum()

        mfi = 100 - (100 / (1 + positive_mf_sum / negative_mf_sum))

        return mfi
