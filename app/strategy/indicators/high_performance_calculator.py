#!/usr/bin/env python3
"""
高性能指标计算器

专门为批量计算优化的指标计算器
"""
import logging
import time
from typing import Dict, List, Optional, Tuple
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
from multiprocessing import cpu_count
import pandas as pd
import numpy as np

from .engine import IndicatorEngine
from ..services.high_performance_data_service import HighPerformanceDataService
from ..indicators.parquet_storage import ParquetIndicatorStorage

logger = logging.getLogger(__name__)


class HighPerformanceIndicatorCalculator:
    """高性能指标计算器"""
    
    def __init__(self, max_workers: int = None):
        """初始化高性能指标计算器
        
        Args:
            max_workers: 最大工作进程数，默认为CPU核心数
        """
        self.max_workers = max_workers or min(cpu_count(), 8)
        self.data_service = HighPerformanceDataService()
        self.storage = ParquetIndicatorStorage()
        
        logger.info(f"初始化高性能指标计算器，最大工作进程数: {self.max_workers}")
    
    def calculate_indicators_for_date_batch(self, date: str, symbols: List[str] = None,
                                          batch_size: int = 500, 
                                          force_recalculate: bool = False) -> Dict:
        """批量计算指定日期的指标
        
        Args:
            date: 目标日期
            symbols: 股票代码列表，None表示所有股票
            batch_size: 批次大小
            force_recalculate: 是否强制重新计算
            
        Returns:
            计算结果统计
        """
        start_time = time.time()
        logger.info(f"开始批量计算日期 {date} 的指标")
        
        try:
            # 检查是否需要重新计算
            if not force_recalculate:
                existing_data = self.storage.load_indicators_by_date(date)
                if not existing_data.empty:
                    logger.info(f"日期 {date} 的指标已存在，跳过计算")
                    return {
                        'success': True,
                        'date': date,
                        'calculated': 0,
                        'skipped': len(existing_data),
                        'failed': 0,
                        'elapsed_time': time.time() - start_time
                    }
            
            # 获取股票列表
            if symbols is None:
                symbols = self.data_service.get_stock_list_for_date(date)
            
            if not symbols:
                logger.warning(f"日期 {date} 没有可用的股票")
                return {
                    'success': False,
                    'error': '没有可用的股票',
                    'elapsed_time': time.time() - start_time
                }
            
            logger.info(f"日期 {date} 需要计算 {len(symbols)} 只股票的指标")
            
            # 批量加载历史数据
            logger.info("批量加载历史数据...")
            historical_data = self.data_service.load_stock_history_batch(
                symbols=symbols,
                end_date=date,
                days=60  # 加载60天历史数据，足够计算所有技术指标
            )
            
            if not historical_data:
                logger.warning("没有加载到历史数据")
                return {
                    'success': False,
                    'error': '没有历史数据',
                    'elapsed_time': time.time() - start_time
                }
            
            logger.info(f"成功加载 {len(historical_data)} 只股票的历史数据")

            # 过滤数据充足的股票
            valid_symbols = []
            insufficient_count = 0
            for symbol, data in historical_data.items():
                if len(data) >= 14:  # 至少需要14天数据计算RSI
                    valid_symbols.append(symbol)
                else:
                    insufficient_count += 1
                    logger.debug(f"股票 {symbol} 数据不足: {len(data)} < 14")

            logger.info(f"数据充足的股票: {len(valid_symbols)} 只，数据不足: {insufficient_count} 只")
            
            if not valid_symbols:
                return {
                    'success': False,
                    'error': '没有数据充足的股票',
                    'elapsed_time': time.time() - start_time
                }
            
            # 分批并行计算
            all_indicators = []
            calculated_count = 0
            failed_count = 0
            
            # 将股票分批处理
            for i in range(0, len(valid_symbols), batch_size):
                batch_symbols = valid_symbols[i:i + batch_size]
                batch_num = i // batch_size + 1
                total_batches = (len(valid_symbols) + batch_size - 1) // batch_size
                
                logger.info(f"处理第 {batch_num}/{total_batches} 批: {len(batch_symbols)} 只股票")
                
                # 准备批次数据
                batch_data = {symbol: historical_data[symbol] for symbol in batch_symbols}
                
                # 并行计算指标
                batch_results = self._calculate_batch_indicators_parallel(
                    batch_data, date
                )
                
                # 处理结果
                for result in batch_results:
                    if result['success']:
                        all_indicators.append(result['indicators'])
                        calculated_count += 1
                    else:
                        failed_count += 1
                        logger.warning(f"股票 {result['symbol']} 计算失败: {result['error']}")
            
            # 保存结果
            if all_indicators:
                combined_indicators = pd.concat(all_indicators, ignore_index=True)
                
                # 保存到存储
                save_success = self.storage.save_indicators_by_date(date, combined_indicators)
                
                if save_success:
                    logger.info(f"成功保存日期 {date} 的指标数据: {len(combined_indicators)} 条记录")
                else:
                    logger.error(f"保存日期 {date} 的指标数据失败")
            
            elapsed_time = time.time() - start_time
            
            result = {
                'success': True,
                'date': date,
                'calculated': calculated_count,
                'failed': failed_count,
                'total_symbols': len(symbols),
                'valid_symbols': len(valid_symbols),
                'elapsed_time': elapsed_time
            }
            
            logger.info(f"日期 {date} 指标计算完成: 成功 {calculated_count}, 失败 {failed_count}, 耗时 {elapsed_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"批量计算日期 {date} 指标失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'elapsed_time': time.time() - start_time
            }
    
    def _calculate_batch_indicators_parallel(self, batch_data: Dict[str, pd.DataFrame], 
                                           target_date: str) -> List[Dict]:
        """并行计算批次指标"""
        results = []
        
        # 使用线程池并行计算（因为主要是CPU密集型任务）
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_symbol = {
                executor.submit(self._calculate_single_stock_indicators, symbol, data, target_date): symbol
                for symbol, data in batch_data.items()
            }
            
            # 收集结果
            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    result = future.result(timeout=30)
                    results.append(result)
                except Exception as e:
                    logger.error(f"计算股票 {symbol} 指标失败: {e}")
                    results.append({
                        'symbol': symbol,
                        'success': False,
                        'error': str(e)
                    })
        
        return results
    
    def _calculate_single_stock_indicators(self, symbol: str, data: pd.DataFrame, 
                                         target_date: str) -> Dict:
        """计算单只股票的指标"""
        try:
            # 创建指标引擎
            engine = IndicatorEngine()
            
            # 计算所有指标
            indicators = engine.calculate_all_indicators(data)
            
            if indicators.empty:
                return {
                    'symbol': symbol,
                    'success': False,
                    'error': '指标计算结果为空'
                }
            
            # 只保留目标日期的数据
            target_indicators = indicators[indicators['date'] == target_date]
            
            if target_indicators.empty:
                return {
                    'symbol': symbol,
                    'success': False,
                    'error': f'目标日期 {target_date} 没有指标数据'
                }
            
            # 添加股票代码
            target_indicators = target_indicators.copy()
            target_indicators['symbol'] = symbol
            
            return {
                'symbol': symbol,
                'success': True,
                'indicators': target_indicators
            }
            
        except Exception as e:
            return {
                'symbol': symbol,
                'success': False,
                'error': str(e)
            }
    
    def calculate_indicators_incremental(self, start_date: str, end_date: str,
                                       symbols: List[str] = None,
                                       force_recalculate: bool = False) -> Dict:
        """增量计算指标
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            symbols: 股票代码列表
            force_recalculate: 是否强制重新计算
            
        Returns:
            计算结果统计
        """
        start_time = time.time()
        logger.info(f"开始增量计算指标: {start_date} 到 {end_date}")
        
        try:
            available_dates = self.data_service.get_available_dates()
            target_dates = [d for d in available_dates if start_date <= d <= end_date]
            
            if not target_dates:
                return {
                    'success': False,
                    'error': f'日期范围 {start_date} 到 {end_date} 内没有可用数据'
                }
            
            logger.info(f"需要处理 {len(target_dates)} 个日期")
            
            total_calculated = 0
            total_failed = 0
            calculated_dates = 0
            failed_dates = 0
            
            for i, date in enumerate(target_dates, 1):
                logger.info(f"处理日期 {date} ({i}/{len(target_dates)})")
                
                result = self.calculate_indicators_for_date_batch(
                    date=date,
                    symbols=symbols,
                    force_recalculate=force_recalculate
                )
                
                if result['success']:
                    calculated_dates += 1
                    total_calculated += result.get('calculated', 0)
                    total_failed += result.get('failed', 0)
                else:
                    failed_dates += 1
                    logger.error(f"日期 {date} 计算失败: {result.get('error', '未知错误')}")
            
            elapsed_time = time.time() - start_time
            
            result = {
                'success': True,
                'start_date': start_date,
                'end_date': end_date,
                'total_dates': len(target_dates),
                'calculated_dates': calculated_dates,
                'failed_dates': failed_dates,
                'total_calculated': total_calculated,
                'total_failed': total_failed,
                'elapsed_time': elapsed_time
            }
            
            logger.info(f"增量计算完成: 成功日期 {calculated_dates}/{len(target_dates)}, 耗时 {elapsed_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"增量计算失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'elapsed_time': time.time() - start_time
            }
