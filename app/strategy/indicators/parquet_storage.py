"""
Parquet格式的指标数据存储管理器

提供高性能的指标数据存储和查询功能
"""
import os
import logging
from pathlib import Path
from typing import Dict, List, Optional, Union
from datetime import datetime, timedelta
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from concurrent.futures import ThreadPoolExecutor
import numpy as np

from app.strategy.config import settings

logger = logging.getLogger(__name__)


class ParquetIndicatorStorage:
    """Parquet格式指标数据存储管理器"""
    
    def __init__(self):
        self.base_path = Path(settings.data.processed_data_path) / "indicators"
        self.by_date_path = self.base_path / "by_date"
        self.by_symbol_path = self.base_path / "by_symbol"
        
        # 确保目录存在
        self._ensure_directories()
        
        # 配置Parquet写入选项
        self.parquet_options = {
            'compression': 'snappy',
            'use_dictionary': True,
            'row_group_size': 50000,
            'data_page_size': 1024 * 1024  # 1MB
        }
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        for path in [self.base_path, self.by_date_path, self.by_symbol_path]:
            path.mkdir(parents=True, exist_ok=True)
    
    def save_indicators_by_date(self, date: str, indicators_data: pd.DataFrame) -> bool:
        """按日期保存指标数据
        
        Args:
            date: 日期字符串 (YYYY-MM-DD)
            indicators_data: 指标数据，包含symbol列和各种指标列
            
        Returns:
            是否保存成功
        """
        try:
            if indicators_data.empty:
                logger.warning(f"日期 {date} 的指标数据为空")
                return False
            
            # 解析日期
            date_obj = datetime.strptime(date, '%Y-%m-%d')
            year = date_obj.year
            month = date_obj.month
            
            # 创建年月目录
            year_month_path = self.by_date_path / str(year) / f"{month:02d}"
            year_month_path.mkdir(parents=True, exist_ok=True)
            
            # 文件路径
            file_path = year_month_path / f"{date.replace('-', '')}.parquet"
            
            # 添加日期列
            data_to_save = indicators_data.copy()
            data_to_save['date'] = date
            
            # 重新排列列顺序，将symbol和date放在前面
            cols = ['symbol', 'date'] + [col for col in data_to_save.columns if col not in ['symbol', 'date']]
            data_to_save = data_to_save[cols]
            
            # 保存为Parquet
            data_to_save.to_parquet(file_path, **self.parquet_options, index=False)
            
            logger.info(f"保存日期 {date} 指标数据成功: {len(data_to_save)} 条记录 -> {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存日期 {date} 指标数据失败: {e}")
            return False
    
    def save_indicators_by_symbol(self, symbol: str, indicators_data: pd.DataFrame) -> bool:
        """按股票代码保存指标数据
        
        Args:
            symbol: 股票代码
            indicators_data: 指标数据，包含date列和各种指标列
            
        Returns:
            是否保存成功
        """
        try:
            if indicators_data.empty:
                logger.warning(f"股票 {symbol} 的指标数据为空")
                return False
            
            # 文件路径
            file_path = self.by_symbol_path / f"{symbol}.parquet"
            
            # 添加股票代码列
            data_to_save = indicators_data.copy()
            data_to_save['symbol'] = symbol
            
            # 确保日期列存在且格式正确
            if 'date' in data_to_save.columns:
                data_to_save['date'] = pd.to_datetime(data_to_save['date']).dt.strftime('%Y-%m-%d')
            
            # 重新排列列顺序
            cols = ['symbol', 'date'] + [col for col in data_to_save.columns if col not in ['symbol', 'date']]
            data_to_save = data_to_save[cols]
            
            # 按日期排序
            data_to_save = data_to_save.sort_values('date')
            
            # 如果文件已存在，需要合并数据
            if file_path.exists():
                existing_data = pd.read_parquet(file_path)
                
                # 移除重复日期的数据
                if 'date' in existing_data.columns:
                    new_dates = set(data_to_save['date'].unique())
                    existing_data = existing_data[~existing_data['date'].isin(new_dates)]
                
                # 合并数据
                combined_data = pd.concat([existing_data, data_to_save], ignore_index=True)
                combined_data = combined_data.sort_values('date')
            else:
                combined_data = data_to_save
            
            # 保存为Parquet
            combined_data.to_parquet(file_path, **self.parquet_options, index=False)
            
            logger.info(f"保存股票 {symbol} 指标数据成功: {len(combined_data)} 条记录 -> {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存股票 {symbol} 指标数据失败: {e}")
            return False
    
    def load_indicators_by_date(self, date: str) -> pd.DataFrame:
        """按日期加载指标数据
        
        Args:
            date: 日期字符串 (YYYY-MM-DD)
            
        Returns:
            指标数据DataFrame
        """
        try:
            # 解析日期
            date_obj = datetime.strptime(date, '%Y-%m-%d')
            year = date_obj.year
            month = date_obj.month
            
            # 文件路径
            file_path = self.by_date_path / str(year) / f"{month:02d}" / f"{date.replace('-', '')}.parquet"
            
            if not file_path.exists():
                logger.warning(f"日期 {date} 的指标数据文件不存在: {file_path}")
                return pd.DataFrame()
            
            # 读取数据
            data = pd.read_parquet(file_path)
            logger.debug(f"加载日期 {date} 指标数据成功: {len(data)} 条记录")
            return data
            
        except Exception as e:
            logger.error(f"加载日期 {date} 指标数据失败: {e}")
            return pd.DataFrame()
    
    def load_indicators_by_symbol(self, symbol: str, start_date: str = None,
                                end_date: str = None) -> pd.DataFrame:
        """按股票代码加载指标数据（从按日期存储的文件中提取）

        Args:
            symbol: 股票代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)

        Returns:
            指标数据DataFrame
        """
        try:
            # 首先尝试从按股票存储的文件读取（向后兼容）
            symbol_file_path = self.by_symbol_path / f"{symbol}.parquet"

            if symbol_file_path.exists():
                # 读取按股票存储的数据
                data = pd.read_parquet(symbol_file_path)

                # 日期过滤
                if 'date' in data.columns:
                    data['date'] = pd.to_datetime(data['date'])

                    if start_date:
                        start_date_obj = pd.to_datetime(start_date)
                        data = data[data['date'] >= start_date_obj]

                    if end_date:
                        end_date_obj = pd.to_datetime(end_date)
                        data = data[data['date'] <= end_date_obj]

                    # 转换回字符串格式
                    data['date'] = data['date'].dt.strftime('%Y-%m-%d')

                logger.debug(f"从按股票文件加载股票 {symbol} 指标数据成功: {len(data)} 条记录")
                return data

            # 如果按股票存储的文件不存在，从按日期存储的文件中提取
            return self._load_symbol_from_date_files(symbol, start_date, end_date)

        except Exception as e:
            logger.error(f"加载股票 {symbol} 指标数据失败: {e}")
            return pd.DataFrame()

    def _load_symbol_from_date_files(self, symbol: str, start_date: str = None,
                                   end_date: str = None) -> pd.DataFrame:
        """从按日期存储的文件中提取特定股票的指标数据"""
        try:
            # 获取可用的日期文件
            available_dates = self._get_available_date_files()

            if not available_dates:
                logger.warning(f"没有找到可用的日期文件")
                return pd.DataFrame()

            # 过滤日期范围
            if start_date:
                available_dates = [d for d in available_dates if d >= start_date]
            if end_date:
                available_dates = [d for d in available_dates if d <= end_date]

            if not available_dates:
                logger.warning(f"日期范围内没有可用数据")
                return pd.DataFrame()

            # 从多个日期文件中提取股票数据
            all_data = []

            for date in available_dates:
                try:
                    date_data = self.load_indicators_by_date(date)
                    if not date_data.empty and 'symbol' in date_data.columns:
                        symbol_data = date_data[date_data['symbol'] == symbol]
                        if not symbol_data.empty:
                            all_data.append(symbol_data)
                except Exception as e:
                    logger.debug(f"读取日期 {date} 数据失败: {e}")
                    continue

            if all_data:
                # 合并所有数据
                combined_data = pd.concat(all_data, ignore_index=True)
                combined_data = combined_data.sort_values('date')

                logger.debug(f"从日期文件提取股票 {symbol} 指标数据成功: {len(combined_data)} 条记录")
                return combined_data
            else:
                logger.warning(f"股票 {symbol} 在指定日期范围内没有指标数据")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"从日期文件提取股票 {symbol} 数据失败: {e}")
            return pd.DataFrame()

    def _get_available_date_files(self) -> List[str]:
        """获取可用的日期文件列表"""
        try:
            dates = []

            for year_dir in sorted(self.by_date_path.iterdir()):
                if not year_dir.is_dir() or not year_dir.name.isdigit():
                    continue

                for month_dir in sorted(year_dir.iterdir()):
                    if not month_dir.is_dir():
                        continue

                    for file_path in sorted(month_dir.glob("*.parquet")):
                        # 从文件名提取日期
                        date_str = file_path.stem
                        if len(date_str) == 8 and date_str.isdigit():
                            formatted_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
                            dates.append(formatted_date)

            return sorted(dates)

        except Exception as e:
            logger.error(f"获取可用日期文件失败: {e}")
            return []
            
        except Exception as e:
            logger.error(f"加载股票 {symbol} 指标数据失败: {e}")
            return pd.DataFrame()
    
    def get_latest_date(self, symbol: str = None) -> Optional[str]:
        """获取最新的指标计算日期
        
        Args:
            symbol: 股票代码，如果为None则返回全局最新日期
            
        Returns:
            最新日期字符串 (YYYY-MM-DD)
        """
        try:
            if symbol:
                # 获取特定股票的最新日期
                data = self.load_indicators_by_symbol(symbol)
                if data.empty or 'date' not in data.columns:
                    return None
                return data['date'].max()
            else:
                # 获取全局最新日期
                latest_date = None
                
                # 遍历年月目录查找最新文件
                for year_dir in sorted(self.by_date_path.iterdir(), reverse=True):
                    if not year_dir.is_dir():
                        continue
                    
                    for month_dir in sorted(year_dir.iterdir(), reverse=True):
                        if not month_dir.is_dir():
                            continue
                        
                        # 查找该月的最新文件
                        parquet_files = sorted([f for f in month_dir.iterdir() if f.suffix == '.parquet'], reverse=True)
                        if parquet_files:
                            # 从文件名提取日期
                            latest_file = parquet_files[0]
                            date_str = latest_file.stem  # YYYYMMDD
                            formatted_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
                            return formatted_date
                
                return latest_date
                
        except Exception as e:
            logger.error(f"获取最新日期失败: {e}")
            return None
    
    def get_available_symbols(self) -> List[str]:
        """获取所有可用的股票代码
        
        Returns:
            股票代码列表
        """
        try:
            symbols = []
            for file_path in self.by_symbol_path.iterdir():
                if file_path.suffix == '.parquet':
                    symbols.append(file_path.stem)
            
            return sorted(symbols)
            
        except Exception as e:
            logger.error(f"获取可用股票代码失败: {e}")
            return []
    
    def get_date_range(self) -> tuple:
        """获取指标数据的日期范围
        
        Returns:
            (最早日期, 最新日期) 元组
        """
        try:
            earliest_date = None
            latest_date = None
            
            # 遍历所有年月目录
            for year_dir in sorted(self.by_date_path.iterdir()):
                if not year_dir.is_dir():
                    continue
                
                for month_dir in sorted(year_dir.iterdir()):
                    if not month_dir.is_dir():
                        continue
                    
                    # 获取该月的所有文件
                    parquet_files = sorted([f for f in month_dir.iterdir() if f.suffix == '.parquet'])
                    
                    if parquet_files:
                        # 最早日期
                        if earliest_date is None:
                            first_file = parquet_files[0]
                            date_str = first_file.stem
                            earliest_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
                        
                        # 最新日期
                        last_file = parquet_files[-1]
                        date_str = last_file.stem
                        latest_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
            
            return earliest_date, latest_date
            
        except Exception as e:
            logger.error(f"获取日期范围失败: {e}")
            return None, None
    
    def cleanup_old_data(self, days_to_keep: int = 365):
        """清理旧的指标数据
        
        Args:
            days_to_keep: 保留的天数
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            deleted_count = 0
            
            # 遍历按日期存储的文件
            for year_dir in self.by_date_path.iterdir():
                if not year_dir.is_dir():
                    continue
                
                year = int(year_dir.name)
                if year < cutoff_date.year:
                    # 删除整个年份目录
                    import shutil
                    shutil.rmtree(year_dir)
                    deleted_count += 1
                    logger.info(f"删除年份目录: {year_dir}")
                elif year == cutoff_date.year:
                    # 检查月份
                    for month_dir in year_dir.iterdir():
                        if not month_dir.is_dir():
                            continue
                        
                        month = int(month_dir.name)
                        if month < cutoff_date.month:
                            import shutil
                            shutil.rmtree(month_dir)
                            deleted_count += 1
                            logger.info(f"删除月份目录: {month_dir}")
            
            logger.info(f"清理完成，删除了 {deleted_count} 个目录")
            
        except Exception as e:
            logger.error(f"清理旧数据失败: {e}")
