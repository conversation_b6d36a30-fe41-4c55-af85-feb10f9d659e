"""
批量离线计算指标脚本 - 高性能版本

预计算所有股票的技术指标并存储到parquet文件中
使用高性能批量计算引擎
"""
import sys
import argparse
import logging
import time
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Dict, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from app.strategy.indicators.high_performance_calculator import HighPerformanceIndicatorCalculator
from app.strategy.services.high_performance_data_service import HighPerformanceDataService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BatchIndicatorCalculationManager:
    """高性能批量指标计算管理器"""

    def __init__(self, max_workers: int = 8):
        """初始化管理器

        Args:
            max_workers: 最大工作进程数
        """
        self.data_service = HighPerformanceDataService()
        self.calculator = HighPerformanceIndicatorCalculator(max_workers=max_workers)

        print(f"🚀 初始化高性能批量指标计算器，工作进程数: {max_workers}")

    def get_available_dates(self) -> List[str]:
        """获取可用的日期列表"""
        return self.data_service.get_available_dates()

    def get_available_stocks(self, date: str = None, limit: Optional[int] = None) -> List[str]:
        """获取可用的股票列表

        Args:
            date: 指定日期，None表示最新日期
            limit: 限制数量

        Returns:
            股票代码列表
        """
        try:
            if date:
                symbols = self.data_service.get_stock_list_for_date(date)
            else:
                symbols = self.data_service.get_latest_stock_list()

            if limit and len(symbols) > limit:
                symbols = symbols[:limit]

            print(f"📊 找到 {len(symbols)} 只股票")
            return symbols

        except Exception as e:
            print(f"❌ 获取股票列表失败: {e}")
            return []

    def calculate_indicators_for_date(self, date: str, symbols: List[str] = None,
                                    batch_size: int = 500,
                                    force_recalculate: bool = False) -> Dict:
        """计算指定日期的指标

        Args:
            date: 目标日期
            symbols: 股票代码列表，None表示所有股票
            batch_size: 批次大小
            force_recalculate: 是否强制重新计算

        Returns:
            计算结果
        """
        print(f"🚀 开始计算日期 {date} 的指标")

        return self.calculator.calculate_indicators_for_date_batch(
            date=date,
            symbols=symbols,
            batch_size=batch_size,
            force_recalculate=force_recalculate
        )

    def batch_calculate_indicators_incremental(self, start_date: str, end_date: str,
                                             symbols: List[str] = None,
                                             force_recalculate: bool = False) -> Dict:
        """增量批量计算指标

        Args:
            start_date: 开始日期
            end_date: 结束日期
            symbols: 股票代码列表，None表示所有股票
            force_recalculate: 是否强制重新计算

        Returns:
            计算结果
        """
        print(f"🚀 开始增量计算指标: {start_date} 到 {end_date}")

        return self.calculator.calculate_indicators_incremental(
            start_date=start_date,
            end_date=end_date,
            symbols=symbols,
            force_recalculate=force_recalculate
        )

    def batch_calculate_all_available_dates(self, symbols: List[str] = None,
                                          force_recalculate: bool = False) -> Dict:
        """计算所有可用日期的指标

        Args:
            symbols: 股票代码列表，None表示所有股票
            force_recalculate: 是否强制重新计算

        Returns:
            计算结果
        """
        available_dates = self.get_available_dates()

        if not available_dates:
            return {
                'success': False,
                'error': '没有可用的日期数据'
            }

        start_date = available_dates[0]
        end_date = available_dates[-1]

        print(f"🚀 计算所有可用日期的指标: {start_date} 到 {end_date} ({len(available_dates)} 个日期)")

        return self.batch_calculate_indicators_incremental(
            start_date=start_date,
            end_date=end_date,
            symbols=symbols,
            force_recalculate=force_recalculate
        )

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='高性能批量计算股票技术指标')

    # 日期参数
    parser.add_argument('--date', help='指定单个日期 (YYYY-MM-DD)')
    parser.add_argument('--start-date', help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--all-dates', action='store_true', help='计算所有可用日期')

    # 股票参数
    parser.add_argument('--symbols', nargs='+', help='指定股票代码列表')
    parser.add_argument('--limit', type=int, help='限制处理的股票数量')

    # 性能参数
    parser.add_argument('--workers', type=int, default=8, help='并行工作进程数 (默认: 8)')
    parser.add_argument('--batch-size', type=int, default=500, help='批次大小 (默认: 500)')

    # 控制参数
    parser.add_argument('--force', action='store_true', help='强制重新计算')
    parser.add_argument('--status', action='store_true', help='显示状态信息')

    args = parser.parse_args()

    print("🚀 高性能批量技术指标计算脚本")
    print("=" * 50)

    try:
        # 创建计算管理器
        manager = BatchIndicatorCalculationManager(max_workers=args.workers)

        # 显示状态信息
        if args.status:
            show_status(manager)
            return

        # 确定计算模式和参数
        if args.date:
            # 单日计算
            symbols = get_symbols_list(args, manager, args.date)

            print(f"📅 计算日期: {args.date}")
            print(f"📊 股票数量: {len(symbols) if symbols else '全部'}")
            print(f"⚙️ 配置: 工作进程={args.workers}, 批次大小={args.batch_size}")

            if args.force:
                print("🔄 强制重新计算模式")

            if not confirm_start():
                return

            # 执行单日计算
            result = manager.calculate_indicators_for_date(
                date=args.date,
                symbols=symbols,
                batch_size=args.batch_size,
                force_recalculate=args.force
            )

            print_single_date_result(result)

        elif args.start_date and args.end_date:
            # 增量计算
            symbols = get_symbols_list(args, manager)

            print(f"📅 计算日期范围: {args.start_date} 到 {args.end_date}")
            print(f"📊 股票数量: {len(symbols) if symbols else '全部'}")
            print(f"⚙️ 配置: 工作进程={args.workers}, 批次大小={args.batch_size}")

            if args.force:
                print("🔄 强制重新计算模式")

            if not confirm_start():
                return

            # 执行增量计算
            result = manager.batch_calculate_indicators_incremental(
                start_date=args.start_date,
                end_date=args.end_date,
                symbols=symbols,
                force_recalculate=args.force
            )

            print_incremental_result(result)

        elif args.all_dates:
            # 计算所有日期
            symbols = get_symbols_list(args, manager)

            print(f"📅 计算所有可用日期")
            print(f"📊 股票数量: {len(symbols) if symbols else '全部'}")
            print(f"⚙️ 配置: 工作进程={args.workers}, 批次大小={args.batch_size}")

            if args.force:
                print("🔄 强制重新计算模式")

            if not confirm_start():
                return

            # 执行全量计算
            result = manager.batch_calculate_all_available_dates(
                symbols=symbols,
                force_recalculate=args.force
            )

            print_incremental_result(result)

        else:
            print("❌ 请指定计算模式:")
            print("  --date YYYY-MM-DD           计算单个日期")
            print("  --start-date --end-date     计算日期范围")
            print("  --all-dates                 计算所有可用日期")
            print("  --status                    显示状态信息")
            return

        # 显示完成信息
        if result.get('success'):
            print("\n💡 现在可以启动Web服务使用预计算的指标:")
            print("python -m uvicorn app.main:app --reload")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        logger.exception("脚本执行异常")


def show_status(manager: BatchIndicatorCalculationManager):
    """显示状态信息"""
    print("📊 数据状态信息")
    print("-" * 30)

    available_dates = manager.get_available_dates()

    if available_dates:
        print(f"📅 可用日期范围: {available_dates[0]} 到 {available_dates[-1]}")
        print(f"📅 总日期数: {len(available_dates)}")
        print(f"📅 最新日期: {available_dates[-1]}")

        # 获取最新日期的股票数量
        latest_symbols = manager.get_available_stocks(available_dates[-1])
        print(f"📊 最新日期股票数: {len(latest_symbols)}")
    else:
        print("❌ 没有找到可用的数据")


def get_symbols_list(args, manager: BatchIndicatorCalculationManager, date: str = None) -> List[str]:
    """获取股票列表"""
    if args.symbols:
        return args.symbols

    symbols = manager.get_available_stocks(date=date, limit=args.limit)
    return symbols if symbols else None


def confirm_start() -> bool:
    """确认开始计算"""
    try:
        confirm = input("\n确认开始计算？(y/n): ").lower().strip()
        return confirm == 'y'
    except (EOFError, KeyboardInterrupt):
        return False


def print_single_date_result(result: Dict):
    """打印单日计算结果"""
    print("\n" + "=" * 50)
    print("📊 单日计算结果")
    print("-" * 30)

    if result.get('success'):
        print(f"📅 日期: {result.get('date')}")
        print(f"✅ 计算成功: {result.get('calculated', 0)} 只股票")
        print(f"❌ 计算失败: {result.get('failed', 0)} 只股票")
        print(f"📊 总股票数: {result.get('total_symbols', 0)}")
        print(f"📈 有效股票数: {result.get('valid_symbols', 0)}")
        print(f"⏱ 耗时: {result.get('elapsed_time', 0):.2f} 秒")

        if result.get('calculated', 0) > 0:
            speed = result.get('calculated', 0) / result.get('elapsed_time', 1)
            print(f"🚀 计算速度: {speed:.1f} 股票/秒")
    else:
        print(f"❌ 计算失败: {result.get('error', '未知错误')}")


def print_incremental_result(result: Dict):
    """打印增量计算结果"""
    print("\n" + "=" * 50)
    print("📊 增量计算结果")
    print("-" * 30)

    if result.get('success'):
        print(f"📅 日期范围: {result.get('start_date')} 到 {result.get('end_date')}")
        print(f"📅 总日期数: {result.get('total_dates', 0)}")
        print(f"✅ 成功日期: {result.get('calculated_dates', 0)}")
        print(f"❌ 失败日期: {result.get('failed_dates', 0)}")
        print(f"📊 总计算股票: {result.get('total_calculated', 0)}")
        print(f"❌ 总失败股票: {result.get('total_failed', 0)}")
        print(f"⏱ 总耗时: {result.get('elapsed_time', 0):.2f} 秒")

        if result.get('total_calculated', 0) > 0:
            speed = result.get('total_calculated', 0) / result.get('elapsed_time', 1)
            print(f"🚀 平均速度: {speed:.1f} 股票/秒")
    else:
        print(f"❌ 计算失败: {result.get('error', '未知错误')}")


if __name__ == "__main__":
    main()
