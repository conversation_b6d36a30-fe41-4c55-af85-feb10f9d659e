"""
高性能指标计算服务

提供技术指标的快速计算和缓存功能
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from functools import lru_cache
import hashlib
import json
from concurrent.futures import ThreadPoolExecutor
import asyncio

from ..indicators.engine import IndicatorEngine
from ..indicators.parquet_storage import ParquetIndicatorStorage
from .data_service import data_service

logger = logging.getLogger(__name__)

class HighPerformanceIndicatorService:
    """高性能指标计算服务"""
    
    def __init__(self):
        self.indicator_engine = IndicatorEngine()
        self.parquet_storage = ParquetIndicatorStorage()
        self.cache_size = 500
        self.executor = ThreadPoolExecutor(max_workers=4)
    
    def _generate_cache_key(self, symbol: str, start_date: str, end_date: str, 
                          indicators: List[str]) -> str:
        """生成缓存键"""
        key_data = {
            'symbol': symbol,
            'start_date': start_date,
            'end_date': end_date,
            'indicators': sorted(indicators)
        }
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    @lru_cache(maxsize=500)
    def calculate_indicators_cached(self, symbol: str, start_date: str, end_date: str,
                                  indicators_tuple: tuple) -> pd.DataFrame:
        """计算指标（带缓存）"""
        try:
            # 加载股票数据
            data = data_service.load_stock_history(symbol, start_date, end_date)
            
            if data.empty:
                logger.warning(f"股票 {symbol} 无数据")
                return pd.DataFrame()
            
            # 转换为列表
            indicators = list(indicators_tuple)
            
            # 计算指标
            if 'all' in indicators:
                result_data = self.indicator_engine.calculate_all_indicators(data)
            else:
                result_data = data.copy()
                for indicator in indicators:
                    result_data = self._calculate_single_indicator(result_data, indicator)
            
            logger.info(f"股票 {symbol} 指标计算完成: {len(result_data)} 条记录")
            return result_data
            
        except Exception as e:
            logger.error(f"计算股票 {symbol} 指标失败: {e}")
            return pd.DataFrame()

    def get_latest_indicators(self, symbol: str, indicators: List[str] = None) -> Dict:
        """获取股票最新的技术指标数据

        Args:
            symbol: 股票代码
            indicators: 指标列表，None表示所有指标

        Returns:
            最新指标数据字典
        """
        try:
            # 从预计算的指标数据中获取最新记录
            parquet_data = self.parquet_storage.load_indicators_by_symbol(symbol)

            if not parquet_data.empty:
                # 获取最新一条记录
                latest = parquet_data.iloc[-1]

                result = {
                    'symbol': symbol,
                    'date': latest.get('date', ''),
                    'indicators': {}
                }

                # 提取指标数据
                for col in parquet_data.columns:
                    if col not in ['symbol', 'date'] and pd.api.types.is_numeric_dtype(parquet_data[col]):
                        value = latest[col]
                        if not pd.isna(value):
                            # 如果指定了特定指标，进行过滤
                            if indicators is None or any(ind.upper() in col.upper() for ind in indicators):
                                result['indicators'][col] = float(value)

                logger.info(f"获取股票 {symbol} 最新指标成功: {len(result['indicators'])} 个指标")
                return result

            # 如果没有预计算数据，返回空结果
            logger.warning(f"股票 {symbol} 没有预计算的指标数据")
            return {
                'symbol': symbol,
                'date': '',
                'indicators': {}
            }

        except Exception as e:
            logger.error(f"获取股票 {symbol} 最新指标失败: {e}")
            return {
                'symbol': symbol,
                'date': '',
                'indicators': {}
            }
    
    def calculate_indicators(self, symbol: str, start_date: str = None,
                           end_date: str = None, indicators: List[str] = None) -> pd.DataFrame:
        """获取技术指标（优先从Parquet读取，兼容实时计算）"""
        try:
            # 首先尝试从Parquet存储读取预计算的指标
            parquet_data = self.parquet_storage.load_indicators_by_symbol(symbol, start_date, end_date)

            if not parquet_data.empty:
                logger.info(f"从预计算Parquet读取股票 {symbol} 指标: {len(parquet_data)} 条记录")

                # 如果指定了特定指标，进行过滤
                if indicators and 'all' not in indicators:
                    available_cols = ['symbol', 'date'] + [col for col in parquet_data.columns
                                                         if any(ind.upper() in col.upper() for ind in indicators)]
                    parquet_data = parquet_data[available_cols]

                return parquet_data

            # 如果Parquet没有数据，尝试从数据库读取（向后兼容）
            try:
                from app.strategy.services.offline_calculator import OfflineIndicatorCalculator
                calculator = OfflineIndicatorCalculator()
                try:
                    db_data = calculator.get_stock_indicators_from_db(symbol, start_date, end_date, limit=250)
                finally:
                    calculator.close()

                if not db_data.empty:
                    logger.info(f"从数据库读取股票 {symbol} 指标: {len(db_data)} 条记录")
                    return db_data
            except Exception as db_e:
                logger.warning(f"数据库读取失败: {db_e}")

            logger.warning(f"股票 {symbol} 指标数据不存在，请先运行批量计算")
            return pd.DataFrame()

        except Exception as e:
            logger.error(f"读取指标失败: {e}")
            return pd.DataFrame()
    
    def _calculate_single_indicator(self, data: pd.DataFrame, indicator: str) -> pd.DataFrame:
        """计算单个指标"""
        try:
            if indicator.upper() == 'MACD':
                return self.indicator_engine._calculate_macd(data)
            elif indicator.upper() == 'RSI':
                return self.indicator_engine._calculate_rsi(data)
            elif indicator.upper() == 'KDJ':
                return self.indicator_engine._calculate_kdj(data)
            elif indicator.upper() == 'BOLL':
                return self.indicator_engine._calculate_boll(data)
            elif indicator.upper() == 'MA':
                return self.indicator_engine._calculate_ma(data)
            elif indicator.upper() == 'EMA':
                return self.indicator_engine._calculate_ema(data)
            elif indicator.upper() == 'CCI':
                return self.indicator_engine._calculate_cci(data)
            elif indicator.upper() == 'WR':
                return self.indicator_engine._calculate_wr(data)
            elif indicator.upper() == 'OBV':
                return self.indicator_engine._calculate_obv(data)
            elif indicator.upper() == 'VOL_MA':
                return self.indicator_engine._calculate_vol_ma(data)
            else:
                logger.warning(f"未知指标: {indicator}")
                return data
                
        except Exception as e:
            logger.error(f"计算指标 {indicator} 失败: {e}")
            return data
    
    async def calculate_batch_indicators(self, symbols: List[str], 
                                       indicators: List[str] = None,
                                       limit: int = 50) -> Dict[str, pd.DataFrame]:
        """批量计算指标"""
        try:
            if indicators is None:
                indicators = ['MACD', 'RSI', 'KDJ', 'BOLL', 'MA']
            
            # 限制并发数量
            symbols = symbols[:limit]
            
            # 并行计算
            loop = asyncio.get_event_loop()
            tasks = []
            
            for symbol in symbols:
                task = loop.run_in_executor(
                    self.executor,
                    self.calculate_indicators,
                    symbol, None, None, indicators
                )
                tasks.append((symbol, task))
            
            results = {}
            for symbol, task in tasks:
                try:
                    result = await task
                    if not result.empty:
                        results[symbol] = result
                except Exception as e:
                    logger.warning(f"计算股票 {symbol} 指标失败: {e}")
            
            logger.info(f"批量计算完成: {len(results)}/{len(symbols)} 只股票")
            return results
            
        except Exception as e:
            logger.error(f"批量计算指标失败: {e}")
            return {}
    
    def get_indicator_summary(self, symbol: str, indicators: List[str] = None) -> Dict:
        """获取指标摘要（优先从Parquet读取）"""
        try:
            # 首先尝试从Parquet存储读取最新数据
            parquet_data = self.parquet_storage.load_indicators_by_symbol(symbol)

            if not parquet_data.empty:
                # 获取最新一条记录
                latest = parquet_data.iloc[-1]

                # 计算涨跌幅
                pct_change = 0.0
                if 'pct_change' in latest.index and pd.notna(latest['pct_change']):
                    pct_change = float(latest['pct_change'])
                elif 'close' in latest.index and 'prev_close' in latest.index:
                    close = latest.get('close', 0)
                    prev_close = latest.get('prev_close', 0)
                    if prev_close > 0:
                        pct_change = (close - prev_close) / prev_close * 100

                summary = {
                    'symbol': symbol,
                    'date': latest.get('date', ''),
                    'close': float(latest.get('close', 0)) if pd.notna(latest.get('close')) else 0.0,
                    'pct_change': pct_change,
                    'indicators': {}
                }

                # 添加主要指标
                self._add_indicator_to_summary(summary, latest, 'MACD')
                self._add_indicator_to_summary(summary, latest, 'RSI')
                self._add_indicator_to_summary(summary, latest, 'KDJ')
                self._add_indicator_to_summary(summary, latest, 'BOLL')

                logger.info(f"从Parquet获取股票 {symbol} 指标摘要成功")
                return summary

            # 如果Parquet没有数据，尝试从数据库读取（向后兼容）
            try:
                from app.strategy.services.offline_calculator import OfflineIndicatorCalculator
                calculator = OfflineIndicatorCalculator()
                try:
                    db_summary = calculator.get_indicator_summary_from_db(symbol)
                finally:
                    calculator.close()

                if db_summary:
                    logger.info(f"从数据库获取股票 {symbol} 指标摘要成功")
                    return db_summary
            except Exception as db_e:
                logger.warning(f"数据库读取摘要失败: {db_e}")

            if db_summary:
                # 转换数据库格式为API格式
                # 安全处理日期格式
                latest_date = db_summary.get('latest_date')
                if latest_date:
                    if hasattr(latest_date, 'strftime'):
                        date_str = latest_date.strftime('%Y-%m-%d')
                    else:
                        date_str = str(latest_date)
                else:
                    date_str = None

                summary = {
                    'symbol': symbol,
                    'date': date_str,
                    'close': float(db_summary.get('close', 0)),
                    'pct_change': float(db_summary.get('pct_change', 0)),
                    'indicators': {}
                }

                # RSI指标
                if db_summary.get('rsi_value') is not None:
                    summary['indicators']['RSI'] = {
                        'value': float(db_summary['rsi_value']),
                        'level': db_summary.get('rsi_level', 'normal')
                    }

                # MACD指标
                if db_summary.get('macd_value') is not None:
                    summary['indicators']['MACD'] = {
                        'value': float(db_summary['macd_value']),
                        'signal': float(db_summary.get('macd_signal', 0)),
                        'trend': db_summary.get('macd_trend', 'neutral')
                    }

                # KDJ指标
                if db_summary.get('kdj_k') is not None:
                    summary['indicators']['KDJ'] = {
                        'K': float(db_summary['kdj_k']),
                        'D': float(db_summary.get('kdj_d', 0)),
                        'J': float(db_summary.get('kdj_j', 0)),
                        'signal': db_summary.get('kdj_signal', 'neutral')
                    }

                # 布林带指标
                if db_summary.get('boll_upper') is not None:
                    summary['indicators']['BOLL'] = {
                        'upper': float(db_summary['boll_upper']),
                        'middle': float(db_summary.get('boll_middle', 0)),
                        'lower': float(db_summary.get('boll_lower', 0)),
                        'position': db_summary.get('boll_position', 'middle'),
                        'width': (float(db_summary['boll_upper']) - float(db_summary.get('boll_lower', 0))) / float(db_summary.get('boll_middle', 1)) if db_summary.get('boll_middle', 0) > 0 else 0.0
                    }

                logger.info(f"从数据库读取股票 {symbol} 指标摘要")
                return summary

            # 如果数据库没有摘要，返回空结果
            logger.warning(f"股票 {symbol} 指标摘要不存在，请先运行批量计算")
            return {}

        except Exception as e:
            logger.error(f"获取股票 {symbol} 指标摘要失败: {e}")
            return {}

    def _add_indicator_to_summary(self, summary: Dict, latest: pd.Series, indicator_name: str):
        """添加指标到摘要"""
        try:
            if indicator_name == 'MACD':
                if 'MACD' in latest.index and pd.notna(latest['MACD']):
                    summary['indicators']['MACD'] = {
                        'value': float(latest['MACD']),
                        'signal': float(latest.get('MACD_Signal', 0)) if pd.notna(latest.get('MACD_Signal')) else 0.0,
                        'histogram': float(latest.get('MACD_Histogram', 0)) if pd.notna(latest.get('MACD_Histogram')) else 0.0,
                        'trend': 'bullish' if latest['MACD'] > latest.get('MACD_Signal', 0) else 'bearish'
                    }

            elif indicator_name == 'RSI':
                rsi_col = None
                for col in latest.index:
                    if 'RSI' in col and pd.notna(latest[col]):
                        rsi_col = col
                        break

                if rsi_col:
                    rsi_value = float(latest[rsi_col])
                    summary['indicators']['RSI'] = {
                        'value': rsi_value,
                        'level': 'overbought' if rsi_value > 70 else 'oversold' if rsi_value < 30 else 'normal'
                    }

            elif indicator_name == 'KDJ':
                if 'KDJ_K' in latest.index and pd.notna(latest['KDJ_K']):
                    k_value = float(latest['KDJ_K'])
                    d_value = float(latest.get('KDJ_D', 0)) if pd.notna(latest.get('KDJ_D')) else 0.0
                    j_value = float(latest.get('KDJ_J', 0)) if pd.notna(latest.get('KDJ_J')) else 0.0

                    summary['indicators']['KDJ'] = {
                        'K': k_value,
                        'D': d_value,
                        'J': j_value,
                        'signal': 'buy' if k_value > d_value and k_value < 20 else 'sell' if k_value < d_value and k_value > 80 else 'hold'
                    }

            elif indicator_name == 'BOLL':
                if 'BOLL_Upper' in latest.index and pd.notna(latest['BOLL_Upper']):
                    close_price = float(latest.get('close', 0))
                    upper = float(latest['BOLL_Upper'])
                    middle = float(latest.get('BOLL_Middle', 0)) if pd.notna(latest.get('BOLL_Middle')) else 0.0
                    lower = float(latest.get('BOLL_Lower', 0)) if pd.notna(latest.get('BOLL_Lower')) else 0.0

                    if close_price > 0:
                        if close_price > upper:
                            position = 'above_upper'
                        elif close_price < lower:
                            position = 'below_lower'
                        else:
                            position = 'middle'
                    else:
                        position = 'unknown'

                    summary['indicators']['BOLL'] = {
                        'upper': upper,
                        'middle': middle,
                        'lower': lower,
                        'position': position
                    }

        except Exception as e:
            logger.error(f"添加指标 {indicator_name} 到摘要失败: {e}")

    def get_latest_indicators_date(self) -> Optional[str]:
        """获取最新的指标计算日期"""
        try:
            return self.parquet_storage.get_latest_date()
        except Exception as e:
            logger.error(f"获取最新指标日期失败: {e}")
            return None

    def get_available_symbols(self) -> List[str]:
        """获取所有可用的股票代码"""
        try:
            return self.parquet_storage.get_available_symbols()
        except Exception as e:
            logger.error(f"获取可用股票代码失败: {e}")
            return []
            
            # MACD指标
            if 'MACD' in latest.index and pd.notna(latest['MACD']):
                summary['indicators']['MACD'] = {
                    'value': float(latest['MACD']),
                    'signal': float(latest['MACD_Signal']) if 'MACD_Signal' in latest.index else 0.0,
                    'histogram': float(latest['MACD_Histogram']) if 'MACD_Histogram' in latest.index else 0.0,
                    'trend': 'bullish' if latest['MACD'] > latest.get('MACD_Signal', 0) else 'bearish'
                }
            
            # RSI指标
            if 'RSI' in latest.index and pd.notna(latest['RSI']):
                rsi_value = float(latest['RSI'])
                summary['indicators']['RSI'] = {
                    'value': rsi_value,
                    'level': 'overbought' if rsi_value > 70 else 'oversold' if rsi_value < 30 else 'normal'
                }
            
            # KDJ指标
            if 'KDJ_K' in latest.index and pd.notna(latest['KDJ_K']):
                k_value = float(latest['KDJ_K'])
                d_value = float(latest['KDJ_D']) if 'KDJ_D' in latest.index else 0.0
                summary['indicators']['KDJ'] = {
                    'K': k_value,
                    'D': d_value,
                    'J': float(latest['KDJ_J']) if 'KDJ_J' in latest.index else 0.0,
                    'signal': 'golden_cross' if k_value > d_value else 'death_cross'
                }
            
            # 布林带指标
            if 'BOLL_Upper' in latest.index and pd.notna(latest['BOLL_Upper']):
                close_price = float(latest['close'])
                upper = float(latest['BOLL_Upper'])
                lower = float(latest['BOLL_Lower']) if 'BOLL_Lower' in latest.index else 0.0
                middle = float(latest['BOLL_Middle']) if 'BOLL_Middle' in latest.index else 0.0
                
                position = 'upper' if close_price > upper else 'lower' if close_price < lower else 'middle'
                
                summary['indicators']['BOLL'] = {
                    'upper': upper,
                    'middle': middle,
                    'lower': lower,
                    'position': position,
                    'width': (upper - lower) / middle if middle > 0 else 0.0
                }
            
            return summary
            
        except Exception as e:
            logger.error(f"获取指标摘要失败: {e}")
            return {}
    
    def get_market_indicators_ranking(self, indicator: str = 'RSI',
                                    limit: int = 100) -> List[Dict]:
        """获取市场指标排行（批量优化版本）"""
        try:
            # 获取最新日期的所有指标数据
            from app.strategy.services.high_performance_data_service import HighPerformanceDataService
            hp_data_service = HighPerformanceDataService()

            available_dates = hp_data_service.get_available_dates()
            if not available_dates:
                logger.warning("没有可用的日期数据")
                return []

            latest_date = available_dates[-1]

            # 批量加载最新日期的所有指标数据
            all_indicators = self.parquet_storage.load_indicators_by_date(latest_date)

            if all_indicators.empty:
                logger.warning(f"日期 {latest_date} 没有指标数据")
                return []

            # 过滤包含指定指标的股票
            if indicator not in all_indicators.columns:
                logger.warning(f"指标 {indicator} 不存在")
                return []

            # 过滤有效数据
            valid_data = all_indicators[
                (all_indicators[indicator].notna()) &
                (all_indicators['symbol'].notna())
            ].copy()

            if valid_data.empty:
                logger.warning(f"没有股票包含有效的 {indicator} 数据")
                return []

            # 计算涨跌幅（如果没有的话）
            if 'pct_change' not in valid_data.columns:
                if 'close' in valid_data.columns and 'prev_close' in valid_data.columns:
                    valid_data['pct_change'] = (
                        (valid_data['close'] - valid_data['prev_close']) / valid_data['prev_close'] * 100
                    ).fillna(0)
                else:
                    valid_data['pct_change'] = 0

            # 按指标值排序
            valid_data = valid_data.sort_values(indicator, ascending=False)

            # 转换为排行格式
            rankings = []
            for _, row in valid_data.head(limit).iterrows():
                ranking_item = {
                    'symbol': row['symbol'],
                    'close': safe_float(row.get('close', 0)),
                    'pct_change': safe_float(row.get('pct_change', 0)),
                    'indicator': indicator,
                    'value': safe_float(row[indicator])
                }

                # 添加特定指标的额外信息
                if indicator == 'RSI':
                    rsi_value = safe_float(row[indicator])
                    ranking_item['level'] = 'overbought' if rsi_value > 70 else 'oversold' if rsi_value < 30 else 'normal'
                elif indicator == 'MACD':
                    macd_signal = safe_float(row.get('MACD_Signal', 0))
                    ranking_item['trend'] = 'bullish' if safe_float(row[indicator]) > macd_signal else 'bearish'
                elif indicator in ['KDJ_K', 'K']:
                    k_value = safe_float(row.get('KDJ_K', row.get('K', 0)))
                    d_value = safe_float(row.get('KDJ_D', row.get('D', 0)))
                    ranking_item['signal'] = 'golden_cross' if k_value > d_value else 'death_cross'

                rankings.append(ranking_item)

            logger.info(f"指标 {indicator} 排行计算完成: {len(rankings)} 只股票 (批量加载)")
            return rankings
            
        except Exception as e:
            logger.error(f"获取指标排行失败: {e}")
            return []
    
    def get_available_indicators(self) -> List[Dict]:
        """获取可用指标列表"""
        return [
            {'name': 'MACD', 'description': 'MACD指标', 'category': 'trend'},
            {'name': 'RSI', 'description': 'RSI相对强弱指标', 'category': 'momentum'},
            {'name': 'KDJ', 'description': 'KDJ随机指标', 'category': 'momentum'},
            {'name': 'BOLL', 'description': '布林带指标', 'category': 'volatility'},
            {'name': 'MA', 'description': '移动平均线', 'category': 'trend'},
            {'name': 'EMA', 'description': '指数移动平均线', 'category': 'trend'},
            {'name': 'CCI', 'description': 'CCI顺势指标', 'category': 'momentum'},
            {'name': 'WR', 'description': 'WR威廉指标', 'category': 'momentum'},
            {'name': 'OBV', 'description': 'OBV能量潮指标', 'category': 'volume'},
            {'name': 'VOL_MA', 'description': '成交量移动平均', 'category': 'volume'}
        ]

# 全局指标服务实例
indicator_service = HighPerformanceIndicatorService()
