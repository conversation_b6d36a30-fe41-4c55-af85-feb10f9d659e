#!/usr/bin/env python3
"""
高性能数据服务

专门为批量指标计算优化的数据加载服务
"""
import os
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)


class HighPerformanceDataService:
    """高性能数据服务"""
    
    def __init__(self, data_root: str = None):
        """初始化高性能数据服务
        
        Args:
            data_root: 数据根目录路径
        """
        if data_root is None:
            # 默认数据路径
            project_root = Path(__file__).parent.parent.parent.parent
            data_root = project_root / "data" / "strategy" / "raw"
        
        self.data_root = Path(data_root)
        self._date_cache = {}
        self._file_cache = {}
        
        logger.info(f"初始化高性能数据服务，数据根目录: {self.data_root}")
    
    def get_available_dates(self) -> List[str]:
        """获取可用的日期列表"""
        if hasattr(self, '_cached_dates'):
            return self._cached_dates
        
        dates = []
        try:
            for year_dir in sorted(self.data_root.iterdir()):
                if not year_dir.is_dir() or not year_dir.name.isdigit():
                    continue
                
                for month_dir in sorted(year_dir.iterdir()):
                    if not month_dir.is_dir():
                        continue
                    
                    for file_path in sorted(month_dir.glob("*.parquet")):
                        # 从文件名提取日期
                        date_str = file_path.stem
                        if len(date_str) == 8 and date_str.isdigit():
                            formatted_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
                            dates.append(formatted_date)
            
            self._cached_dates = sorted(dates)
            logger.info(f"找到 {len(self._cached_dates)} 个可用日期")
            return self._cached_dates
            
        except Exception as e:
            logger.error(f"获取可用日期失败: {e}")
            return []
    
    def get_date_file_path(self, date: str) -> Optional[Path]:
        """获取指定日期的文件路径"""
        try:
            # 转换日期格式
            date_obj = datetime.strptime(date, "%Y-%m-%d")
            year = date_obj.strftime("%Y")
            month = date_obj.strftime("%m")
            filename = date_obj.strftime("%Y%m%d.parquet")
            
            file_path = self.data_root / year / month / filename
            
            if file_path.exists():
                return file_path
            else:
                logger.warning(f"日期 {date} 的数据文件不存在: {file_path}")
                return None
                
        except Exception as e:
            logger.error(f"获取日期 {date} 文件路径失败: {e}")
            return None
    
    def load_batch_data_for_date_range(self, start_date: str, end_date: str, 
                                     symbols: List[str] = None) -> pd.DataFrame:
        """批量加载日期范围内的数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            symbols: 股票代码列表，None表示加载所有股票
            
        Returns:
            合并后的DataFrame
        """
        logger.info(f"批量加载数据: {start_date} 到 {end_date}, 股票数: {len(symbols) if symbols else '全部'}")
        
        try:
            available_dates = self.get_available_dates()
            target_dates = [d for d in available_dates if start_date <= d <= end_date]
            
            if not target_dates:
                logger.warning(f"日期范围 {start_date} 到 {end_date} 内没有可用数据")
                return pd.DataFrame()
            
            # 并行加载多个日期的数据
            all_data = []
            
            def load_single_date_data(date: str) -> pd.DataFrame:
                """加载单个日期的数据"""
                file_path = self.get_date_file_path(date)
                if not file_path:
                    return pd.DataFrame()
                
                try:
                    # 读取parquet文件
                    df = pd.read_parquet(file_path)
                    
                    # 过滤指定股票
                    if symbols:
                        df = df[df['symbol'].isin(symbols)]
                    
                    if not df.empty:
                        logger.debug(f"加载日期 {date} 数据: {len(df)} 条记录")
                    
                    return df
                    
                except Exception as e:
                    logger.error(f"加载日期 {date} 数据失败: {e}")
                    return pd.DataFrame()
            
            # 使用线程池并行加载
            max_workers = min(8, len(target_dates))
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_date = {
                    executor.submit(load_single_date_data, date): date 
                    for date in target_dates
                }
                
                for future in as_completed(future_to_date):
                    date = future_to_date[future]
                    try:
                        data = future.result(timeout=30)
                        if not data.empty:
                            all_data.append(data)
                    except Exception as e:
                        logger.error(f"处理日期 {date} 数据失败: {e}")
            
            if all_data:
                # 合并所有数据
                combined_data = pd.concat(all_data, ignore_index=True)
                combined_data = combined_data.sort_values(['symbol', 'date'])
                
                logger.info(f"批量加载完成: {len(combined_data)} 条记录, {combined_data['symbol'].nunique()} 只股票")
                return combined_data
            else:
                logger.warning("没有加载到任何数据")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"批量加载数据失败: {e}")
            return pd.DataFrame()
    
    def load_stock_history_batch(self, symbols: List[str], end_date: str = None, 
                               days: int = 60) -> Dict[str, pd.DataFrame]:
        """批量加载多只股票的历史数据
        
        Args:
            symbols: 股票代码列表
            end_date: 结束日期
            days: 历史天数
            
        Returns:
            股票代码到DataFrame的映射
        """
        logger.info(f"批量加载 {len(symbols)} 只股票的历史数据")
        
        try:
            available_dates = self.get_available_dates()
            
            if not available_dates:
                return {}
            
            # 确定日期范围
            if end_date is None:
                end_date = available_dates[-1]
            
            if end_date not in available_dates:
                # 找到最接近的日期
                valid_dates = [d for d in available_dates if d <= end_date]
                if valid_dates:
                    end_date = valid_dates[-1]
                else:
                    logger.warning(f"没有找到 {end_date} 之前的有效日期")
                    return {}
            
            # 计算开始日期
            end_idx = available_dates.index(end_date)
            start_idx = max(0, end_idx - days + 1)
            start_date = available_dates[start_idx]
            
            logger.info(f"加载日期范围: {start_date} 到 {end_date}")
            
            # 批量加载数据
            all_data = self.load_batch_data_for_date_range(start_date, end_date, symbols)
            
            if all_data.empty:
                return {}
            
            # 按股票分组
            result = {}
            for symbol in symbols:
                symbol_data = all_data[all_data['symbol'] == symbol].copy()
                if not symbol_data.empty:
                    symbol_data = symbol_data.sort_values('date')
                    result[symbol] = symbol_data
                    logger.debug(f"股票 {symbol}: {len(symbol_data)} 条记录")
                else:
                    logger.warning(f"股票 {symbol} 没有数据")
            
            logger.info(f"批量加载完成: {len(result)} 只股票有数据")
            return result
            
        except Exception as e:
            logger.error(f"批量加载股票历史数据失败: {e}")
            return {}
    
    def get_stock_list_for_date(self, date: str) -> List[str]:
        """获取指定日期的股票列表"""
        try:
            file_path = self.get_date_file_path(date)
            if not file_path:
                return []
            
            df = pd.read_parquet(file_path, columns=['symbol'])
            symbols = df['symbol'].unique().tolist()
            
            logger.info(f"日期 {date} 有 {len(symbols)} 只股票")
            return sorted(symbols)
            
        except Exception as e:
            logger.error(f"获取日期 {date} 股票列表失败: {e}")
            return []
    
    def get_latest_stock_list(self) -> List[str]:
        """获取最新的股票列表"""
        try:
            available_dates = self.get_available_dates()
            if not available_dates:
                return []
            
            latest_date = available_dates[-1]
            return self.get_stock_list_for_date(latest_date)
            
        except Exception as e:
            logger.error(f"获取最新股票列表失败: {e}")
            return []
