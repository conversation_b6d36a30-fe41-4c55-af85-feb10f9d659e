#!/usr/bin/env python3
"""
调试股票指标API的具体错误

直接调用相关函数来定位strftime错误的具体位置
"""
import sys
from pathlib import Path
import traceback

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def debug_indicator_service():
    """调试指标服务"""
    print("🔍 调试指标服务...")
    
    try:
        from app.strategy.services.indicator_service import HighPerformanceIndicatorService
        
        service = HighPerformanceIndicatorService()
        symbol = "300394"
        
        print(f"测试股票: {symbol}")
        
        # 测试calculate_indicators方法
        print("\n1. 测试 calculate_indicators 方法...")
        try:
            result = service.calculate_indicators(symbol)
            print(f"✅ 成功: {len(result)} 条记录")
            
            if not result.empty:
                print(f"列名: {list(result.columns)}")
                print(f"数据类型:\n{result.dtypes}")
                print(f"前几行数据:")
                print(result.head(2))
            
        except Exception as e:
            print(f"❌ calculate_indicators 失败: {e}")
            print("详细错误:")
            traceback.print_exc()
        
        # 测试get_latest_indicators方法
        print("\n2. 测试 get_latest_indicators 方法...")
        try:
            result = service.get_latest_indicators(symbol)
            print(f"✅ 成功: {len(result.get('indicators', {}))} 个指标")
            print(f"日期: {result.get('date', 'N/A')}")
            
        except Exception as e:
            print(f"❌ get_latest_indicators 失败: {e}")
            print("详细错误:")
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ 指标服务初始化失败: {e}")
        traceback.print_exc()
        return False


def debug_parquet_storage():
    """调试Parquet存储"""
    print("\n🔍 调试Parquet存储...")
    
    try:
        from app.strategy.indicators.parquet_storage import ParquetIndicatorStorage
        
        storage = ParquetIndicatorStorage()
        symbol = "300394"
        
        print(f"测试股票: {symbol}")
        
        # 测试load_indicators_by_symbol方法
        print("\n1. 测试 load_indicators_by_symbol 方法...")
        try:
            result = storage.load_indicators_by_symbol(symbol)
            print(f"✅ 成功: {len(result)} 条记录")
            
            if not result.empty:
                print(f"列名: {list(result.columns)}")
                print(f"日期列类型: {result['date'].dtype}")
                print(f"日期样本: {result['date'].head(3).tolist()}")
            
        except Exception as e:
            print(f"❌ load_indicators_by_symbol 失败: {e}")
            print("详细错误:")
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ Parquet存储初始化失败: {e}")
        traceback.print_exc()
        return False


def debug_api_endpoint():
    """调试API端点"""
    print("\n🔍 调试API端点...")
    
    try:
        # 模拟API调用
        from app.routers.quantitative import safe_float
        from app.strategy.services.indicator_service import HighPerformanceIndicatorService
        import pandas as pd
        
        service = HighPerformanceIndicatorService()
        symbol = "300394"
        
        print(f"测试股票: {symbol}")
        
        # 模拟API逻辑
        print("\n1. 调用 calculate_indicators...")
        data = service.calculate_indicators(symbol, None, None, None)
        
        if data.empty:
            print("❌ 没有指标数据")
            return False
        
        print(f"✅ 获取到数据: {len(data)} 条记录")
        
        # 模拟数据转换逻辑
        print("\n2. 测试数据转换...")
        indicators_data = []
        
        for i, (_, row) in enumerate(data.iterrows()):
            if i >= 2:  # 只测试前2行
                break
                
            print(f"\n处理第 {i+1} 行数据...")
            
            # 安全处理日期格式
            date_value = row['date']
            print(f"  日期值: {date_value} (类型: {type(date_value)})")
            
            if pd.notna(date_value):
                if hasattr(date_value, 'strftime'):
                    print("  日期有strftime方法")
                    date_str = date_value.strftime('%Y-%m-%d')
                    print(f"  格式化后: {date_str}")
                else:
                    print("  日期没有strftime方法，转为字符串")
                    date_str = str(date_value)
                    print(f"  转换后: {date_str}")
            else:
                print("  日期为空")
                date_str = None
            
            indicator_item = {
                "date": date_str,
                "close": safe_float(row['close']) if pd.notna(row['close']) else 0.0
            }
            
            # 添加其他指标
            for col in data.columns:
                if col not in ['symbol', 'date', 'open', 'high', 'low', 'close', 'volume', 'amount']:
                    value = row[col]
                    if pd.notna(value):
                        indicator_item[col] = safe_float(value)
                    else:
                        indicator_item[col] = None
            
            indicators_data.append(indicator_item)
            print(f"  ✅ 第 {i+1} 行处理成功")
        
        print(f"\n✅ 数据转换成功: {len(indicators_data)} 条记录")
        return True
        
    except Exception as e:
        print(f"❌ API端点调试失败: {e}")
        print("详细错误:")
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 调试股票指标API错误")
    print("="*50)
    
    results = []
    
    # 调试指标服务
    results.append(debug_indicator_service())
    
    # 调试Parquet存储
    results.append(debug_parquet_storage())
    
    # 调试API端点
    results.append(debug_api_endpoint())
    
    # 总结
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n" + "="*50)
    print("🎯 调试结果总结")
    print("="*50)
    
    test_names = ["指标服务", "Parquet存储", "API端点"]
    
    for i, (name, success) in enumerate(zip(test_names, results)):
        status = "✅ 正常" if success else "❌ 异常"
        print(f"  {name}: {status}")
    
    print(f"\n📊 总体状态: {success_count}/{total_count} 正常")
    
    if success_count == total_count:
        print("\n🎉 所有组件都正常！错误可能在其他地方")
    else:
        print(f"\n🔧 发现 {total_count - success_count} 个组件有问题")
    
    return success_count == total_count


if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断调试")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 调试异常: {e}")
        traceback.print_exc()
        sys.exit(1)
