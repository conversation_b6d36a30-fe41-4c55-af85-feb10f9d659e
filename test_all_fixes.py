#!/usr/bin/env python3
"""
测试所有修复的综合脚本
"""
import sys
from pathlib import Path
import requests
import json

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_safe_functions():
    """测试安全转换函数"""
    print("🧪 测试安全转换函数...")
    
    try:
        from app.routers.quantitative import safe_float, safe_dict_values
        import math
        
        # 测试各种边界情况
        test_cases = [
            (1.5, 1.5),
            (float('nan'), 0.0),
            (float('inf'), 0.0),
            (-float('inf'), 0.0),
            ("invalid", 0.0),
            (None, 0.0)
        ]
        
        for input_val, expected in test_cases:
            result = safe_float(input_val)
            assert result == expected, f"safe_float({input_val}) = {result}, expected {expected}"
        
        print("✅ safe_float 测试通过")
        
        # 测试复杂数据结构
        test_data = {
            'normal': 1.5,
            'nan': float('nan'),
            'inf': float('inf'),
            'nested': {
                'value': float('nan'),
                'list': [1.0, float('inf'), 2.0]
            }
        }
        
        safe_data = safe_dict_values(test_data)
        assert safe_data['normal'] == 1.5
        assert safe_data['nan'] == 0.0
        assert safe_data['inf'] == 0.0
        assert safe_data['nested']['value'] == 0.0
        assert safe_data['nested']['list'][1] == 0.0
        
        print("✅ safe_dict_values 测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 安全函数测试失败: {e}")
        return False


def test_api_endpoints():
    """测试API端点"""
    print("\n🧪 测试API端点...")
    
    base_url = "http://localhost:8000"
    
    try:
        # 测试健康检查
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code != 200:
            print("❌ 服务器未运行，请先启动: python -m uvicorn app.main:app --reload")
            return False
        
        print("✅ 服务器运行正常")
        
        # 测试形态类型API
        response = requests.get(f"{base_url}/api/quantitative/patterns/types", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 形态类型API: {data.get('total_patterns', 0)} 个形态")
            
            # 检查互斥组
            exclusive_groups = data.get('mutually_exclusive_groups', [])
            print(f"  📊 互斥形态组: {len(exclusive_groups)} 组")
            for group in exclusive_groups:
                print(f"    - {group['name']}: {', '.join(group['patterns'])}")
        else:
            print(f"❌ 形态类型API失败: {response.status_code}")
        
        # 测试股票指标API（修复后的）
        test_symbol = "000001"
        response = requests.get(f"{base_url}/api/quantitative/stocks/{test_symbol}/indicators", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 股票指标API修复成功")
            
            # 检查数据结构
            if 'indicators' in data and len(data['indicators']) > 0:
                first_indicator = data['indicators'][0]
                if 'date' in first_indicator:
                    print(f"  📅 日期格式正确: {first_indicator['date']}")
                if 'close' in first_indicator:
                    print(f"  💰 价格数据正常: {first_indicator['close']}")
        else:
            print(f"❌ 股票指标API失败: {response.status_code}")
        
        # 测试形态筛选API（修复后的）
        response = requests.get(f"{base_url}/api/quantitative/patterns/screening?signal_type=BULLISH&min_confidence=0.6&limit=5", timeout=15)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 形态筛选API修复成功")
            print(f"  📊 分析股票: {data.get('total_analyzed', 0)} 只")
            print(f"  🎯 匹配股票: {data.get('total_matched', 0)} 只")
        else:
            print(f"❌ 形态筛选API失败: {response.status_code}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请先启动服务器:")
        print("   python -m uvicorn app.main:app --reload")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False


def test_page_access():
    """测试页面访问"""
    print("\n🧪 测试页面访问...")
    
    base_url = "http://localhost:8000"
    
    pages_to_test = [
        ("/", "首页"),
        ("/quantitative/stocks", "股票列表"),
        ("/quantitative/indicators", "技术指标"),
        ("/quantitative/patterns", "K线形态"),
        ("/quantitative/market", "市场概览")
    ]
    
    try:
        success_count = 0
        for path, name in pages_to_test:
            try:
                response = requests.get(f"{base_url}{path}", timeout=10)
                if response.status_code == 200:
                    print(f"✅ {name}页面正常")
                    success_count += 1
                else:
                    print(f"❌ {name}页面错误: {response.status_code}")
            except Exception as e:
                print(f"❌ {name}页面访问失败: {e}")
        
        print(f"\n📊 页面测试结果: {success_count}/{len(pages_to_test)} 个页面正常")
        return success_count == len(pages_to_test)
        
    except Exception as e:
        print(f"❌ 页面测试失败: {e}")
        return False


def show_fix_summary():
    """显示修复总结"""
    print("\n" + "="*60)
    print("🎉 修复总结")
    print("="*60)
    
    print("\n✅ 已修复的问题:")
    print("1. 股票详情页面日期格式化错误")
    print("   - 修复了 'str' object has no attribute 'strftime' 错误")
    print("   - 添加了安全的日期处理逻辑")
    print("   - 使用 safe_float() 处理所有浮点数")
    
    print("\n2. K线形态筛选优化")
    print("   - 将按钮组改为多选下拉框")
    print("   - 增加了更多形态类型（21种）")
    print("   - 添加了形态分类和互斥提示")
    print("   - 新增形态类型API端点")
    
    print("\n3. 导航栏增强")
    print("   - 在首页顶部添加了量化分析下拉菜单")
    print("   - 包含股票列表、技术指标、K线形态、市场概览")
    print("   - 统一了导航体验")
    
    print("\n4. JSON序列化安全")
    print("   - 添加了 safe_float() 和 safe_dict_values() 函数")
    print("   - 处理 NaN、Inf 等特殊浮点数值")
    print("   - 确保所有API返回数据可正常序列化")
    
    print("\n🚀 新增功能:")
    print("- 形态类型查询API: /api/quantitative/patterns/types")
    print("- 互斥形态组提示")
    print("- 更丰富的形态分类（反转、持续、中性）")
    print("- 改进的用户界面体验")
    
    print("\n💡 使用建议:")
    print("1. 启动服务器: python -m uvicorn app.main:app --reload")
    print("2. 访问首页: http://localhost:8000")
    print("3. 点击导航栏的'量化分析'查看各功能模块")
    print("4. 在K线形态页面使用新的多选下拉框")


def main():
    """主函数"""
    print("🚀 综合修复测试")
    print("="*50)
    
    results = []
    
    # 测试安全函数
    results.append(test_safe_functions())
    
    # 测试API端点
    results.append(test_api_endpoints())
    
    # 测试页面访问
    results.append(test_page_access())
    
    # 显示修复总结
    show_fix_summary()
    
    # 汇总结果
    print(f"\n📊 测试结果汇总:")
    print(f"  安全函数: {'✅ 通过' if results[0] else '❌ 失败'}")
    print(f"  API端点: {'✅ 通过' if results[1] else '❌ 失败'}")
    print(f"  页面访问: {'✅ 通过' if results[2] else '❌ 失败'}")
    
    success_count = sum(results)
    total_count = len(results)
    
    if success_count == total_count:
        print(f"\n🎉 所有测试通过! ({success_count}/{total_count})")
        print("系统已完全修复，可以正常使用！")
    else:
        print(f"\n⚠️ 部分测试失败: {success_count}/{total_count}")
        if not results[1] or not results[2]:
            print("请确保服务器正在运行: python -m uvicorn app.main:app --reload")
    
    return success_count == total_count


if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
