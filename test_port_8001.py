#!/usr/bin/env python3
"""
测试8001端口服务器的股票详情页面

专门测试您提到的8001端口服务器
"""
import sys
import time
import requests
import json
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_stock_detail_page(symbol="600010", port=8001):
    """测试股票详情页面的所有API"""
    print(f"🧪 测试股票 {symbol} 在端口 {port} 的详情页面...")
    
    base_url = f"http://127.0.0.1:{port}"
    
    # 测试的API端点
    endpoints = [
        {
            "name": "股票K线数据",
            "url": f"/api/quantitative/stocks/{symbol}/kline?limit=120",
            "timeout": 15
        },
        {
            "name": "股票技术指标",
            "url": f"/api/quantitative/stocks/{symbol}/indicators",
            "timeout": 15
        },
        {
            "name": "股票最新指标",
            "url": f"/api/quantitative/stocks/{symbol}/indicators?latest_only=true",
            "timeout": 10
        },
        {
            "name": "股票摘要",
            "url": f"/api/quantitative/stocks/{symbol}/summary",
            "timeout": 10
        },
        {
            "name": "股票形态检测",
            "url": f"/api/quantitative/stocks/{symbol}/patterns",
            "timeout": 20
        }
    ]
    
    print(f"🌐 测试服务器: {base_url}")
    
    # 首先测试服务器连接
    try:
        health_response = requests.get(f"{base_url}/health", timeout=5)
        if health_response.status_code == 200:
            print("✅ 服务器连接正常")
        else:
            print(f"⚠️ 服务器响应异常: {health_response.status_code}")
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return False
    
    results = {}
    all_success = True
    
    for endpoint in endpoints:
        print(f"\n📊 测试 {endpoint['name']}...")
        
        try:
            start_time = time.time()
            response = requests.get(f"{base_url}{endpoint['url']}", timeout=endpoint['timeout'])
            end_time = time.time()
            
            elapsed = end_time - start_time
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    
                    # 分析响应数据
                    data_info = analyze_response_data(data, endpoint['name'])
                    
                    print(f"  ✅ 成功: {elapsed:.2f}s")
                    print(f"  📊 数据: {data_info}")
                    
                    results[endpoint['name']] = {
                        'success': True,
                        'elapsed': elapsed,
                        'data_info': data_info,
                        'response_size': len(str(data))
                    }
                    
                except json.JSONDecodeError as e:
                    print(f"  ❌ JSON解析失败: {e}")
                    print(f"  📝 响应内容: {response.text[:200]}...")
                    results[endpoint['name']] = {
                        'success': False,
                        'error': f'JSON解析失败: {e}'
                    }
                    all_success = False
                    
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
                try:
                    error_data = response.json()
                    error_detail = error_data.get('detail', '未知错误')
                    print(f"  📝 错误详情: {error_detail}")
                except:
                    print(f"  📝 响应内容: {response.text[:200]}...")
                
                results[endpoint['name']] = {
                    'success': False,
                    'error': f'HTTP {response.status_code}',
                    'response_text': response.text[:500]
                }
                all_success = False
                
        except requests.exceptions.Timeout:
            print(f"  ❌ 请求超时 (>{endpoint['timeout']}s)")
            results[endpoint['name']] = {
                'success': False,
                'error': '请求超时'
            }
            all_success = False
            
        except Exception as e:
            print(f"  ❌ 请求失败: {e}")
            results[endpoint['name']] = {
                'success': False,
                'error': str(e)
            }
            all_success = False
    
    return results, all_success


def analyze_response_data(data, endpoint_name):
    """分析响应数据结构"""
    try:
        if endpoint_name == "股票K线数据":
            if isinstance(data, dict) and 'kline' in data:
                kline_data = data['kline']
                if isinstance(kline_data, list) and len(kline_data) > 0:
                    first_item = kline_data[0]
                    fields = list(first_item.keys()) if isinstance(first_item, dict) else []
                    return f"{len(kline_data)} 条K线记录, 字段: {fields}"
                else:
                    return f"K线数据为空"
            else:
                return f"K线数据格式异常: {type(data)}"
                
        elif endpoint_name == "股票技术指标":
            if isinstance(data, dict) and 'indicators' in data:
                indicators = data['indicators']
                if isinstance(indicators, list) and len(indicators) > 0:
                    first_item = indicators[0]
                    if isinstance(first_item, dict):
                        indicator_fields = [k for k in first_item.keys() if k not in ['symbol', 'date', 'close']]
                        return f"{len(indicators)} 条指标记录, {len(indicator_fields)} 个指标"
                    else:
                        return f"指标数据格式异常"
                else:
                    return f"指标数据为空"
            else:
                return f"指标响应格式异常: {list(data.keys()) if isinstance(data, dict) else type(data)}"
                
        elif endpoint_name == "股票最新指标":
            if isinstance(data, dict):
                if 'indicators' in data:
                    indicators = data['indicators']
                    if isinstance(indicators, dict):
                        return f"{len(indicators)} 个最新指标值"
                    else:
                        return f"最新指标格式异常: {type(indicators)}"
                else:
                    return f"响应字段: {list(data.keys())}"
            else:
                return f"响应格式异常: {type(data)}"
                
        elif endpoint_name == "股票摘要":
            if isinstance(data, dict):
                return f"摘要字段: {list(data.keys())}"
            else:
                return f"摘要格式异常: {type(data)}"
                
        elif endpoint_name == "股票形态检测":
            if isinstance(data, dict) and 'patterns' in data:
                patterns = data['patterns']
                if isinstance(patterns, list):
                    return f"{len(patterns)} 个检测到的形态"
                else:
                    return f"形态数据格式异常: {type(patterns)}"
            else:
                return f"形态响应格式异常: {list(data.keys()) if isinstance(data, dict) else type(data)}"
        
        else:
            return "数据结构正常"
            
    except Exception as e:
        return f"分析失败: {e}"


def test_multiple_stocks_on_8001():
    """测试多个股票在8001端口"""
    print("🚀 测试8001端口服务器的股票详情页面")
    print("="*60)
    
    # 测试的股票列表
    test_symbols = ["600010", "300394", "000001", "000002", "600000"]
    
    all_results = {}
    overall_success = True
    
    for i, symbol in enumerate(test_symbols, 1):
        print(f"\n{'='*50}")
        print(f"测试股票 {i}/{len(test_symbols)}: {symbol}")
        print('='*50)
        
        results, success = test_stock_detail_page(symbol, port=8001)
        all_results[symbol] = results
        
        if not success:
            overall_success = False
        
        # 简要统计
        success_count = sum(1 for r in results.values() if r.get('success', False))
        total_count = len(results)
        
        status = "✅" if success else "❌"
        print(f"\n📊 股票 {symbol} 测试结果: {status} {success_count}/{total_count} 成功")
        
        # 如果有失败的，显示错误信息
        if not success:
            print("❌ 失败的API:")
            for name, result in results.items():
                if not result.get('success', False):
                    error = result.get('error', '未知错误')
                    print(f"  - {name}: {error}")
    
    return all_results, overall_success


def print_final_summary(all_results, overall_success):
    """打印最终总结"""
    print("\n" + "="*60)
    print("🎉 测试总结")
    print("="*60)
    
    # 按API端点统计
    endpoint_stats = {}
    
    for symbol, results in all_results.items():
        for endpoint, result in results.items():
            if endpoint not in endpoint_stats:
                endpoint_stats[endpoint] = {'success': 0, 'total': 0}
            
            endpoint_stats[endpoint]['total'] += 1
            if result.get('success', False):
                endpoint_stats[endpoint]['success'] += 1
    
    print("\n📊 API端点成功率:")
    for endpoint, stats in endpoint_stats.items():
        success_rate = stats['success'] / stats['total'] * 100
        status = "✅" if success_rate == 100 else "⚠️" if success_rate >= 50 else "❌"
        print(f"  {status} {endpoint}: {stats['success']}/{stats['total']} ({success_rate:.1f}%)")
    
    # 按股票统计
    print(f"\n📈 股票测试结果:")
    for symbol, results in all_results.items():
        success_count = sum(1 for r in results.values() if r.get('success', False))
        total_count = len(results)
        success_rate = success_count / total_count * 100
        status = "✅" if success_rate == 100 else "⚠️" if success_rate >= 50 else "❌"
        print(f"  {status} {symbol}: {success_count}/{total_count} ({success_rate:.1f}%)")
    
    # 总体统计
    total_tests = sum(len(results) for results in all_results.values())
    total_success = sum(
        sum(1 for r in results.values() if r.get('success', False))
        for results in all_results.values()
    )
    
    overall_rate = total_success / total_tests * 100
    
    print(f"\n🎯 总体成功率: {total_success}/{total_tests} ({overall_rate:.1f}%)")
    
    if overall_success:
        print("🎉 所有测试都通过了！股票详情页面已完全修复！")
        print("\n💡 现在可以正常访问:")
        for symbol in all_results.keys():
            print(f"  http://127.0.0.1:8001/quantitative/stocks/{symbol}")
    elif overall_rate >= 80:
        print("✅ 大部分测试通过，系统基本正常！")
        print("⚠️ 少数API可能还有问题，请检查错误信息")
    elif overall_rate >= 50:
        print("⚠️ 部分测试失败，需要进一步检查！")
        print("🔧 建议检查服务器日志和错误详情")
    else:
        print("❌ 多数测试失败，需要重点修复！")
        print("🔧 请检查服务器配置和代码修复")


def main():
    """主函数"""
    try:
        all_results, overall_success = test_multiple_stocks_on_8001()
        print_final_summary(all_results, overall_success)
        
        print(f"\n💡 修复说明:")
        print("1. ✅ 修复了所有strftime日期格式化错误")
        print("2. ✅ 添加了安全的浮点数处理")
        print("3. ✅ 改进了错误处理和日志记录")
        print("4. ✅ 统一了日期格式处理逻辑")
        
        if overall_success:
            print("\n🎉 修复完成！股票详情页面现在应该可以正常工作了！")
        else:
            print("\n🔧 如果仍有问题，请检查服务器日志获取更多信息")
        
        return overall_success
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        return False
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
