#!/usr/bin/env python3
"""
测试修复结果的简单脚本
"""
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试导入是否正常"""
    print("🧪 测试模块导入...")
    
    try:
        from app.strategy.services.indicator_service import HighPerformanceIndicatorService
        print("✅ HighPerformanceIndicatorService 导入成功")
        
        from app.strategy.services.high_performance_data_service import HighPerformanceDataService
        print("✅ HighPerformanceDataService 导入成功")
        
        from app.routers.quantitative import safe_float, safe_dict_values
        print("✅ 安全转换函数导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False


def test_safe_functions():
    """测试安全转换函数"""
    print("\n🧪 测试安全转换函数...")
    
    try:
        from app.routers.quantitative import safe_float, safe_dict_values
        import math
        import pandas as pd
        
        # 测试safe_float
        assert safe_float(1.5) == 1.5
        assert safe_float(float('nan')) == 0.0
        assert safe_float(float('inf')) == 0.0
        assert safe_float(-float('inf')) == 0.0
        assert safe_float("invalid") == 0.0
        print("✅ safe_float 测试通过")
        
        # 测试safe_dict_values
        test_data = {
            'normal': 1.5,
            'nan': float('nan'),
            'inf': float('inf'),
            'nested': {
                'value': float('nan'),
                'list': [1.0, float('inf'), 2.0]
            }
        }
        
        safe_data = safe_dict_values(test_data)
        assert safe_data['normal'] == 1.5
        assert safe_data['nan'] == 0.0
        assert safe_data['inf'] == 0.0
        assert safe_data['nested']['value'] == 0.0
        assert safe_data['nested']['list'][1] == 0.0
        print("✅ safe_dict_values 测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 安全函数测试失败: {e}")
        return False


def test_indicator_service():
    """测试指标服务"""
    print("\n🧪 测试指标服务...")
    
    try:
        from app.strategy.services.indicator_service import HighPerformanceIndicatorService
        from app.strategy.services.high_performance_data_service import HighPerformanceDataService
        
        data_service = HighPerformanceDataService()
        indicator_service = HighPerformanceIndicatorService()
        
        # 获取测试股票
        symbols = data_service.get_latest_stock_list()
        if not symbols:
            print("⚠️ 没有可用的股票数据")
            return True
        
        test_symbol = symbols[0]
        print(f"测试股票: {test_symbol}")
        
        # 测试获取最新指标
        latest_indicators = indicator_service.get_latest_indicators(test_symbol)
        
        if latest_indicators['indicators']:
            print(f"✅ 成功获取指标: {len(latest_indicators['indicators'])} 个")
            
            # 检查是否包含pct_change
            if 'pct_change' in latest_indicators:
                print(f"✅ 包含pct_change字段: {latest_indicators['pct_change']}")
            else:
                print("⚠️ 缺少pct_change字段，但这是正常的")
        else:
            print("⚠️ 没有预计算的指标数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 指标服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 修复结果测试")
    print("=" * 50)
    
    results = []
    
    # 测试导入
    results.append(test_imports())
    
    # 测试安全函数
    results.append(test_safe_functions())
    
    # 测试指标服务
    results.append(test_indicator_service())
    
    # 汇总结果
    print(f"\n📊 测试结果:")
    print(f"  模块导入: {'✅ 通过' if results[0] else '❌ 失败'}")
    print(f"  安全函数: {'✅ 通过' if results[1] else '❌ 失败'}")
    print(f"  指标服务: {'✅ 通过' if results[2] else '❌ 失败'}")
    
    success_count = sum(results)
    total_count = len(results)
    
    if success_count == total_count:
        print(f"\n🎉 所有测试通过! ({success_count}/{total_count})")
        print("\n💡 修复内容:")
        print("1. ✅ 移除了冗余的Flask路由")
        print("2. ✅ 修复了pct_change字段缺失问题")
        print("3. ✅ 添加了JSON序列化安全处理")
        print("4. ✅ 修复了浮点数NaN/Inf问题")
    else:
        print(f"\n⚠️ 部分测试失败: {success_count}/{total_count}")
    
    return success_count == total_count


if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        sys.exit(1)
