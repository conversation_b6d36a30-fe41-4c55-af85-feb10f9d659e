#!/usr/bin/env python3
"""
股票详情页面修复测试脚本

专门测试股票详情页面的各个API端点
"""
import sys
import time
import requests
import json
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_stock_api_endpoints(symbol="300394"):
    """测试股票相关的所有API端点"""
    print(f"🧪 测试股票 {symbol} 的所有API端点...")
    
    base_url = "http://localhost:8000"
    
    # 测试的API端点
    endpoints = [
        {
            "name": "股票K线数据",
            "url": f"/api/quantitative/stocks/{symbol}/kline?limit=120",
            "timeout": 10
        },
        {
            "name": "股票技术指标",
            "url": f"/api/quantitative/stocks/{symbol}/indicators",
            "timeout": 10
        },
        {
            "name": "股票最新指标",
            "url": f"/api/quantitative/stocks/{symbol}/indicators?latest_only=true",
            "timeout": 10
        },
        {
            "name": "股票摘要",
            "url": f"/api/quantitative/stocks/{symbol}/summary",
            "timeout": 10
        },
        {
            "name": "股票形态检测",
            "url": f"/api/quantitative/stocks/{symbol}/patterns",
            "timeout": 15
        }
    ]
    
    results = {}
    
    for endpoint in endpoints:
        print(f"\n📊 测试 {endpoint['name']}...")
        
        try:
            start_time = time.time()
            response = requests.get(f"{base_url}{endpoint['url']}", timeout=endpoint['timeout'])
            end_time = time.time()
            
            elapsed = end_time - start_time
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    
                    # 检查数据结构
                    data_info = analyze_response_data(data, endpoint['name'])
                    
                    print(f"  ✅ 成功: {elapsed:.2f}s")
                    print(f"  📊 数据: {data_info}")
                    
                    results[endpoint['name']] = {
                        'success': True,
                        'elapsed': elapsed,
                        'data_info': data_info
                    }
                    
                except json.JSONDecodeError as e:
                    print(f"  ❌ JSON解析失败: {e}")
                    results[endpoint['name']] = {
                        'success': False,
                        'error': f'JSON解析失败: {e}'
                    }
                    
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"  📝 错误详情: {error_data.get('detail', '未知错误')}")
                except:
                    print(f"  📝 响应内容: {response.text[:200]}...")
                
                results[endpoint['name']] = {
                    'success': False,
                    'error': f'HTTP {response.status_code}'
                }
                
        except requests.exceptions.Timeout:
            print(f"  ❌ 请求超时 (>{endpoint['timeout']}s)")
            results[endpoint['name']] = {
                'success': False,
                'error': '请求超时'
            }
            
        except Exception as e:
            print(f"  ❌ 请求失败: {e}")
            results[endpoint['name']] = {
                'success': False,
                'error': str(e)
            }
    
    return results


def analyze_response_data(data, endpoint_name):
    """分析响应数据结构"""
    try:
        if endpoint_name == "股票K线数据":
            if isinstance(data, list):
                return f"{len(data)} 条K线记录"
            elif isinstance(data, dict) and 'kline' in data:
                return f"{len(data['kline'])} 条K线记录"
            else:
                return "未知K线数据格式"
                
        elif endpoint_name == "股票技术指标":
            if isinstance(data, dict):
                if 'indicators' in data:
                    indicators = data['indicators']
                    if isinstance(indicators, list):
                        return f"{len(indicators)} 条指标记录"
                    else:
                        return f"指标数据: {type(indicators).__name__}"
                else:
                    return f"响应字段: {list(data.keys())}"
            else:
                return f"数据类型: {type(data).__name__}"
                
        elif endpoint_name == "股票最新指标":
            if isinstance(data, dict):
                if 'indicators' in data:
                    indicators = data['indicators']
                    if isinstance(indicators, dict):
                        return f"{len(indicators)} 个指标值"
                    else:
                        return f"指标类型: {type(indicators).__name__}"
                else:
                    return f"响应字段: {list(data.keys())}"
            else:
                return f"数据类型: {type(data).__name__}"
                
        elif endpoint_name == "股票摘要":
            if isinstance(data, dict):
                return f"摘要字段: {list(data.keys())}"
            else:
                return f"数据类型: {type(data).__name__}"
                
        elif endpoint_name == "股票形态检测":
            if isinstance(data, dict):
                if 'patterns' in data:
                    patterns = data['patterns']
                    if isinstance(patterns, list):
                        return f"{len(patterns)} 个形态"
                    else:
                        return f"形态类型: {type(patterns).__name__}"
                else:
                    return f"响应字段: {list(data.keys())}"
            else:
                return f"数据类型: {type(data).__name__}"
        
        else:
            return "数据结构正常"
            
    except Exception as e:
        return f"分析失败: {e}"


def test_multiple_stocks():
    """测试多个股票"""
    print("\n🧪 测试多个股票的API...")
    
    test_symbols = ["300394", "000001", "000002", "600000", "600036"]
    
    all_results = {}
    
    for symbol in test_symbols:
        print(f"\n{'='*50}")
        print(f"测试股票: {symbol}")
        print('='*50)
        
        results = test_stock_api_endpoints(symbol)
        all_results[symbol] = results
        
        # 简要统计
        success_count = sum(1 for r in results.values() if r.get('success', False))
        total_count = len(results)
        
        print(f"\n📊 股票 {symbol} 测试结果: {success_count}/{total_count} 成功")
    
    return all_results


def print_summary(all_results):
    """打印测试总结"""
    print("\n" + "="*60)
    print("🎉 测试总结")
    print("="*60)
    
    # 按API端点统计
    endpoint_stats = {}
    
    for symbol, results in all_results.items():
        for endpoint, result in results.items():
            if endpoint not in endpoint_stats:
                endpoint_stats[endpoint] = {'success': 0, 'total': 0}
            
            endpoint_stats[endpoint]['total'] += 1
            if result.get('success', False):
                endpoint_stats[endpoint]['success'] += 1
    
    print("\n📊 API端点成功率:")
    for endpoint, stats in endpoint_stats.items():
        success_rate = stats['success'] / stats['total'] * 100
        status = "✅" if success_rate == 100 else "⚠️" if success_rate >= 50 else "❌"
        print(f"  {status} {endpoint}: {stats['success']}/{stats['total']} ({success_rate:.1f}%)")
    
    # 按股票统计
    print(f"\n📈 股票测试结果:")
    for symbol, results in all_results.items():
        success_count = sum(1 for r in results.values() if r.get('success', False))
        total_count = len(results)
        success_rate = success_count / total_count * 100
        status = "✅" if success_rate == 100 else "⚠️" if success_rate >= 50 else "❌"
        print(f"  {status} {symbol}: {success_count}/{total_count} ({success_rate:.1f}%)")
    
    # 总体统计
    total_tests = sum(len(results) for results in all_results.values())
    total_success = sum(
        sum(1 for r in results.values() if r.get('success', False))
        for results in all_results.values()
    )
    
    overall_rate = total_success / total_tests * 100
    
    print(f"\n🎯 总体成功率: {total_success}/{total_tests} ({overall_rate:.1f}%)")
    
    if overall_rate == 100:
        print("🎉 所有测试都通过了！股票详情页面已完全修复！")
    elif overall_rate >= 80:
        print("✅ 大部分测试通过，系统基本正常！")
    elif overall_rate >= 50:
        print("⚠️ 部分测试失败，需要进一步检查！")
    else:
        print("❌ 多数测试失败，需要重点修复！")


def main():
    """主函数"""
    print("🚀 股票详情页面修复测试")
    print("="*50)
    
    # 检查服务器状态
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ 服务器未运行，请先启动:")
            print("   python -m uvicorn app.main:app --reload")
            return
    except:
        print("❌ 无法连接到服务器，请先启动:")
        print("   python -m uvicorn app.main:app --reload")
        return
    
    print("✅ 服务器运行正常，开始测试...\n")
    
    # 执行测试
    all_results = test_multiple_stocks()
    
    # 打印总结
    print_summary(all_results)
    
    print(f"\n💡 修复说明:")
    print("1. ✅ 修复了所有strftime日期格式化错误")
    print("2. ✅ 添加了安全的浮点数处理")
    print("3. ✅ 改进了错误处理和日志记录")
    print("4. ✅ 统一了日期格式处理逻辑")


if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
