#!/usr/bin/env python3
"""
高性能增量更新指标脚本

专门优化的高性能指标计算脚本，特点：
1. 批量加载parquet数据
2. 并行计算指标
3. 内存优化
4. 高效的数据处理

使用示例:
python scripts/update_indicators_high_performance.py --date 2025-08-01
python scripts/update_indicators_high_performance.py --days 3
python scripts/update_indicators_high_performance.py --start-date 2025-07-01 --end-date 2025-08-01
"""
import sys
import os
import argparse
import logging
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.strategy.indicators.high_performance_calculator import HighPerformanceIndicatorCalculator
from app.strategy.services.high_performance_data_service import HighPerformanceDataService

# 确保日志目录存在
os.makedirs('logs', exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/high_performance_update.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='高性能增量更新股票技术指标')
    
    # 日期参数
    parser.add_argument('--date', help='指定单个日期 (YYYY-MM-DD)')
    parser.add_argument('--start-date', help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--days', type=int, help='最近N天')
    
    # 股票参数
    parser.add_argument('--symbols', help='指定股票代码，用逗号分隔')
    parser.add_argument('--limit', type=int, help='限制股票数量')
    
    # 性能参数
    parser.add_argument('--workers', type=int, default=8, help='并行工作进程数 (默认: 8)')
    parser.add_argument('--batch-size', type=int, default=500, help='批次大小 (默认: 500)')
    
    # 控制参数
    parser.add_argument('--force', action='store_true', help='强制重新计算')
    parser.add_argument('--status', action='store_true', help='显示状态信息')
    
    args = parser.parse_args()
    
    print("🚀 高性能股票技术指标计算器")
    print("=" * 50)
    
    try:
        # 创建服务实例
        data_service = HighPerformanceDataService()
        calculator = HighPerformanceIndicatorCalculator(max_workers=args.workers)
        
        # 显示状态信息
        if args.status:
            show_status(data_service)
            return
        
        # 确定日期范围
        start_date, end_date = determine_date_range(args, data_service)
        
        if not start_date or not end_date:
            print("❌ 无法确定有效的日期范围")
            return
        
        print(f"📅 计算日期范围: {start_date} 到 {end_date}")
        
        # 获取股票列表
        symbols = get_symbol_list(args, data_service)
        
        if symbols:
            print(f"📊 指定股票: {len(symbols)} 只")
        else:
            print("📊 计算所有股票")
        
        # 显示配置信息
        print(f"⚙️ 配置: 工作进程={args.workers}, 批次大小={args.batch_size}")
        
        if args.force:
            print("🔄 强制重新计算模式")
        
        # 确认开始
        if not confirm_start():
            return
        
        # 开始计算
        start_time = time.time()
        
        if start_date == end_date:
            # 单日计算
            result = calculator.calculate_indicators_for_date_batch(
                date=start_date,
                symbols=symbols,
                batch_size=args.batch_size,
                force_recalculate=args.force
            )
            
            print_single_date_result(result)
        else:
            # 增量计算
            result = calculator.calculate_indicators_incremental(
                start_date=start_date,
                end_date=end_date,
                symbols=symbols,
                force_recalculate=args.force
            )
            
            print_incremental_result(result)
        
        total_time = time.time() - start_time
        print(f"\n⏱ 总耗时: {total_time:.2f} 秒")
        
        if result.get('success'):
            print("\n🎉 计算完成！")
        else:
            print(f"\n❌ 计算失败: {result.get('error', '未知错误')}")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        logger.exception("脚本执行异常")


def show_status(data_service: HighPerformanceDataService):
    """显示状态信息"""
    print("📊 数据状态信息")
    print("-" * 30)
    
    # 获取可用日期
    available_dates = data_service.get_available_dates()
    
    if available_dates:
        print(f"📅 可用日期范围: {available_dates[0]} 到 {available_dates[-1]}")
        print(f"📅 总日期数: {len(available_dates)}")
        print(f"📅 最新日期: {available_dates[-1]}")
        
        # 获取最新日期的股票数量
        latest_symbols = data_service.get_stock_list_for_date(available_dates[-1])
        print(f"📊 最新日期股票数: {len(latest_symbols)}")
    else:
        print("❌ 没有找到可用的数据")


def determine_date_range(args, data_service: HighPerformanceDataService) -> tuple:
    """确定日期范围"""
    available_dates = data_service.get_available_dates()
    
    if not available_dates:
        print("❌ 没有可用的数据日期")
        return None, None
    
    if args.date:
        # 指定单个日期
        if args.date in available_dates:
            return args.date, args.date
        else:
            print(f"❌ 指定日期 {args.date} 不在可用日期范围内")
            return None, None
    
    elif args.start_date and args.end_date:
        # 指定日期范围
        return args.start_date, args.end_date
    
    elif args.days:
        # 最近N天
        end_date = available_dates[-1]
        if args.days <= len(available_dates):
            start_date = available_dates[-args.days]
        else:
            start_date = available_dates[0]
        return start_date, end_date
    
    else:
        # 默认最近1天
        latest_date = available_dates[-1]
        return latest_date, latest_date


def get_symbol_list(args, data_service: HighPerformanceDataService) -> List[str]:
    """获取股票列表"""
    if args.symbols:
        symbols = [s.strip() for s in args.symbols.split(',')]
        return symbols
    
    return None  # None表示所有股票


def confirm_start() -> bool:
    """确认开始计算"""
    try:
        confirm = input("\n确认开始计算？(y/n): ").lower().strip()
        return confirm == 'y'
    except (EOFError, KeyboardInterrupt):
        return False


def print_single_date_result(result: Dict):
    """打印单日计算结果"""
    print("\n" + "=" * 50)
    print("📊 单日计算结果")
    print("-" * 30)
    
    if result.get('success'):
        print(f"📅 日期: {result.get('date')}")
        print(f"✅ 计算成功: {result.get('calculated', 0)} 只股票")
        print(f"❌ 计算失败: {result.get('failed', 0)} 只股票")
        print(f"📊 总股票数: {result.get('total_symbols', 0)}")
        print(f"📈 有效股票数: {result.get('valid_symbols', 0)}")
        print(f"⏱ 耗时: {result.get('elapsed_time', 0):.2f} 秒")
        
        if result.get('calculated', 0) > 0:
            speed = result.get('calculated', 0) / result.get('elapsed_time', 1)
            print(f"🚀 计算速度: {speed:.1f} 股票/秒")
    else:
        print(f"❌ 计算失败: {result.get('error', '未知错误')}")


def print_incremental_result(result: Dict):
    """打印增量计算结果"""
    print("\n" + "=" * 50)
    print("📊 增量计算结果")
    print("-" * 30)
    
    if result.get('success'):
        print(f"📅 日期范围: {result.get('start_date')} 到 {result.get('end_date')}")
        print(f"📅 总日期数: {result.get('total_dates', 0)}")
        print(f"✅ 成功日期: {result.get('calculated_dates', 0)}")
        print(f"❌ 失败日期: {result.get('failed_dates', 0)}")
        print(f"📊 总计算股票: {result.get('total_calculated', 0)}")
        print(f"❌ 总失败股票: {result.get('total_failed', 0)}")
        print(f"⏱ 总耗时: {result.get('elapsed_time', 0):.2f} 秒")
        
        if result.get('total_calculated', 0) > 0:
            speed = result.get('total_calculated', 0) / result.get('elapsed_time', 1)
            print(f"🚀 平均速度: {speed:.1f} 股票/秒")
    else:
        print(f"❌ 计算失败: {result.get('error', '未知错误')}")


if __name__ == '__main__':
    main()
