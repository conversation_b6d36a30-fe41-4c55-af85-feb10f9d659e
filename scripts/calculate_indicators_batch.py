#!/usr/bin/env python3
"""
批量计算股票技术指标脚本

支持多种计算模式：
1. 按日期计算 - 计算指定日期的所有股票指标
2. 增量计算 - 从最新日期开始增量计算
3. 按股票计算 - 计算指定股票的历史指标
4. 全量重新计算 - 重新计算所有指标

使用示例:
python scripts/calculate_indicators_batch.py --mode date --date 2025-01-15
python scripts/calculate_indicators_batch.py --mode incremental --days 7
python scripts/calculate_indicators_batch.py --mode symbols --symbols 000001,000002
python scripts/calculate_indicators_batch.py --mode full --force
"""
import sys
import os
import argparse
import logging
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.strategy.indicators.batch_calculator import BatchIndicatorCalculator
from app.strategy.services.data_service import HighPerformanceDataService
from app.strategy.indicators.parquet_storage import ParquetIndicatorStorage

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/batch_indicators.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)


def calculate_by_date(args):
    """按日期计算指标"""
    calculator = BatchIndicatorCalculator(max_workers=args.workers)
    
    if args.date:
        dates = [args.date]
    elif args.start_date and args.end_date:
        # 生成日期范围
        start = datetime.strptime(args.start_date, '%Y-%m-%d')
        end = datetime.strptime(args.end_date, '%Y-%m-%d')
        dates = []
        current = start
        while current <= end:
            dates.append(current.strftime('%Y-%m-%d'))
            current += timedelta(days=1)
    else:
        # 默认计算昨天
        yesterday = datetime.now() - timedelta(days=1)
        dates = [yesterday.strftime('%Y-%m-%d')]
    
    symbols = args.symbols.split(',') if args.symbols else None
    
    print(f"🚀 开始按日期计算指标")
    print(f"📅 日期: {dates}")
    print(f"📊 股票: {len(symbols) if symbols else '全部'}")
    print(f"⚙️ 工作进程: {args.workers}")
    print(f"🔄 强制重算: {args.force}")
    
    total_results = {
        'total_dates': len(dates),
        'success_dates': 0,
        'failed_dates': 0,
        'total_symbols': 0,
        'calculated_symbols': 0
    }
    
    for date in dates:
        print(f"\n📅 计算日期: {date}")
        result = calculator.calculate_indicators_for_date(
            date=date,
            symbols=symbols,
            force_recalculate=args.force
        )
        
        if result['success']:
            total_results['success_dates'] += 1
            total_results['total_symbols'] += result.get('total_symbols', 0)
            total_results['calculated_symbols'] += result.get('calculated', 0)
            print(f"✅ 成功: {result.get('calculated', 0)} 只股票, 耗时 {result.get('elapsed_time', 0):.2f}s")
        else:
            total_results['failed_dates'] += 1
            print(f"❌ 失败: {result.get('error', '未知错误')}")
    
    print(f"\n📊 总计结果:")
    print(f"  日期: {total_results['success_dates']}/{total_results['total_dates']} 成功")
    print(f"  股票: {total_results['calculated_symbols']} 只计算完成")


def calculate_incremental(args):
    """增量计算指标"""
    calculator = BatchIndicatorCalculator(max_workers=args.workers)
    
    # 确定开始日期
    if args.start_date:
        start_date = args.start_date
    elif args.days:
        start_date = (datetime.now() - timedelta(days=args.days)).strftime('%Y-%m-%d')
    else:
        start_date = None  # 从最新日期开始
    
    end_date = args.end_date or datetime.now().strftime('%Y-%m-%d')
    symbols = args.symbols.split(',') if args.symbols else None
    
    print(f"🚀 开始增量计算指标")
    print(f"📅 开始日期: {start_date or '自动检测'}")
    print(f"📅 结束日期: {end_date}")
    print(f"📊 股票: {len(symbols) if symbols else '全部'}")
    print(f"⚙️ 工作进程: {args.workers}")
    
    result = calculator.calculate_indicators_incremental(
        start_date=start_date,
        end_date=end_date,
        symbols=symbols
    )
    
    if result['success']:
        print(f"\n✅ 增量计算完成:")
        print(f"  总日期: {result['total_dates']}")
        print(f"  成功日期: {result['calculated_dates']}")
        print(f"  失败日期: {result['failed_dates']}")
        print(f"  耗时: {result['elapsed_time']:.2f}s")
    else:
        print(f"\n❌ 增量计算失败: {result.get('error', '未知错误')}")


def calculate_by_symbols(args):
    """按股票计算指标"""
    calculator = BatchIndicatorCalculator(max_workers=args.workers)
    
    if args.symbols:
        symbols = args.symbols.split(',')
    else:
        # 获取所有股票
        data_service = HighPerformanceDataService()
        stock_list = data_service.get_stock_list()
        symbols = [stock['symbol'] for stock in stock_list]
        
        if args.limit:
            symbols = symbols[:args.limit]
    
    print(f"🚀 开始按股票计算指标")
    print(f"📊 股票数量: {len(symbols)}")
    print(f"📅 开始日期: {args.start_date or '全部历史'}")
    print(f"📅 结束日期: {args.end_date or '最新'}")
    print(f"⚙️ 工作进程: {args.workers}")
    print(f"🔄 强制重算: {args.force}")
    
    result = calculator.calculate_indicators_for_symbols(
        symbols=symbols,
        start_date=args.start_date,
        end_date=args.end_date,
        force_recalculate=args.force
    )
    
    if result['success']:
        print(f"\n✅ 股票指标计算完成:")
        print(f"  总股票: {result['total_symbols']}")
        print(f"  成功: {result['calculated']}")
        print(f"  失败: {result['failed']}")
        print(f"  耗时: {result['elapsed_time']:.2f}s")
    else:
        print(f"\n❌ 股票指标计算失败: {result.get('error', '未知错误')}")


def show_status():
    """显示当前状态"""
    storage = ParquetIndicatorStorage()
    
    print("📊 指标数据状态:")
    
    # 获取日期范围
    earliest_date, latest_date = storage.get_date_range()
    print(f"  日期范围: {earliest_date} 到 {latest_date}")
    
    # 获取股票数量
    symbols = storage.get_available_symbols()
    print(f"  股票数量: {len(symbols)}")
    
    # 显示最近几天的数据
    if latest_date:
        print(f"\n📅 最近数据:")
        for i in range(5):
            check_date = datetime.strptime(latest_date, '%Y-%m-%d') - timedelta(days=i)
            check_date_str = check_date.strftime('%Y-%m-%d')
            data = storage.load_indicators_by_date(check_date_str)
            if not data.empty:
                print(f"  {check_date_str}: {len(data)} 只股票")


def main():
    parser = argparse.ArgumentParser(description='批量计算股票技术指标')
    
    # 基本参数
    parser.add_argument('--mode', choices=['date', 'incremental', 'symbols', 'status'], 
                       default='incremental', help='计算模式')
    parser.add_argument('--workers', type=int, default=4, help='工作进程数')
    parser.add_argument('--force', action='store_true', help='强制重新计算')
    
    # 日期参数
    parser.add_argument('--date', help='指定日期 (YYYY-MM-DD)')
    parser.add_argument('--start-date', help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--days', type=int, help='最近N天')
    
    # 股票参数
    parser.add_argument('--symbols', help='股票代码，逗号分隔')
    parser.add_argument('--limit', type=int, help='限制股票数量')
    
    args = parser.parse_args()
    
    try:
        # 确保日志目录存在
        os.makedirs('logs', exist_ok=True)
        
        if args.mode == 'date':
            calculate_by_date(args)
        elif args.mode == 'incremental':
            calculate_incremental(args)
        elif args.mode == 'symbols':
            calculate_by_symbols(args)
        elif args.mode == 'status':
            show_status()
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        logger.error(f"执行失败: {e}")
        print(f"❌ 执行失败: {e}")


if __name__ == '__main__':
    main()
