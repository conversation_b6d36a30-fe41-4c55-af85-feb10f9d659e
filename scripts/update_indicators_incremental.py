#!/usr/bin/env python3
"""
增量更新股票技术指标脚本

专门用于每日增量更新指标数据，支持：
1. 自动检测最新日期
2. 增量计算缺失的指标
3. 错误重试机制
4. 性能监控

使用示例:
python scripts/update_indicators_incremental.py
python scripts/update_indicators_incremental.py --date 2025-01-15
python scripts/update_indicators_incremental.py --days 3 --symbols 000001,000002
"""
import sys
import os
import argparse
import logging
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.strategy.indicators.batch_calculator import BatchIndicatorCalculator
from app.strategy.indicators.parquet_storage import ParquetIndicatorStorage
from app.strategy.services.data_service import HighPerformanceDataService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/incremental_update.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)


class IncrementalUpdater:
    """增量更新器"""
    
    def __init__(self, max_workers: int = 4):
        self.calculator = BatchIndicatorCalculator(max_workers=max_workers)
        self.storage = ParquetIndicatorStorage()
        self.data_service = HighPerformanceDataService()
        
        # 重试配置
        self.max_retries = 3
        self.retry_delay = 5  # 秒
    
    def update_latest(self, target_date: str = None, symbols: List[str] = None) -> Dict[str, Any]:
        """更新到最新日期
        
        Args:
            target_date: 目标日期，如果为None则更新到今天
            symbols: 股票代码列表
            
        Returns:
            更新结果
        """
        start_time = time.time()
        
        if target_date is None:
            target_date = datetime.now().strftime('%Y-%m-%d')
        
        logger.info(f"开始增量更新到日期: {target_date}")
        
        # 获取最新已计算日期
        latest_calculated = self.storage.get_latest_date()
        if latest_calculated:
            start_date = (datetime.strptime(latest_calculated, '%Y-%m-%d') + timedelta(days=1)).strftime('%Y-%m-%d')
            logger.info(f"最新已计算日期: {latest_calculated}, 从 {start_date} 开始更新")
        else:
            # 如果没有历史数据，从7天前开始
            start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
            logger.info(f"没有历史数据，从 {start_date} 开始计算")
        
        # 检查是否需要更新
        if start_date > target_date:
            logger.info("已经是最新数据，无需更新")
            return {
                'success': True,
                'message': '已经是最新数据',
                'updated_dates': 0,
                'elapsed_time': time.time() - start_time
            }
        
        # 执行增量计算
        result = self.calculator.calculate_indicators_incremental(
            start_date=start_date,
            end_date=target_date,
            symbols=symbols
        )
        
        result['elapsed_time'] = time.time() - start_time
        return result
    
    def update_specific_date(self, date: str, symbols: List[str] = None, 
                           force: bool = False) -> Dict[str, Any]:
        """更新指定日期
        
        Args:
            date: 日期字符串
            symbols: 股票代码列表
            force: 是否强制重新计算
            
        Returns:
            更新结果
        """
        logger.info(f"更新指定日期: {date}")
        
        # 检查日期是否有原始数据
        daily_data = self.data_service.load_daily_data(date)
        if daily_data.empty:
            return {
                'success': False,
                'error': f'日期 {date} 没有原始股票数据',
                'date': date
            }
        
        # 执行计算
        result = self.calculator.calculate_indicators_for_date(
            date=date,
            symbols=symbols,
            force_recalculate=force
        )
        
        return result
    
    def update_recent_days(self, days: int, symbols: List[str] = None) -> Dict[str, Any]:
        """更新最近N天
        
        Args:
            days: 天数
            symbols: 股票代码列表
            
        Returns:
            更新结果
        """
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=days-1)).strftime('%Y-%m-%d')
        
        logger.info(f"更新最近 {days} 天: {start_date} 到 {end_date}")
        
        result = self.calculator.calculate_indicators_incremental(
            start_date=start_date,
            end_date=end_date,
            symbols=symbols
        )
        
        return result
    
    def retry_failed_dates(self, max_retry_days: int = 7) -> Dict[str, Any]:
        """重试失败的日期
        
        Args:
            max_retry_days: 最大重试天数
            
        Returns:
            重试结果
        """
        logger.info(f"检查最近 {max_retry_days} 天的失败日期")
        
        # 获取最近的日期范围
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=max_retry_days-1)).strftime('%Y-%m-%d')
        
        # 检查哪些日期缺失指标数据
        missing_dates = []
        current_date = datetime.strptime(start_date, '%Y-%m-%d')
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
        
        while current_date <= end_date_obj:
            date_str = current_date.strftime('%Y-%m-%d')
            
            # 检查是否有原始数据但缺少指标数据
            daily_data = self.data_service.load_daily_data(date_str)
            if not daily_data.empty:
                indicator_data = self.storage.load_indicators_by_date(date_str)
                if indicator_data.empty:
                    missing_dates.append(date_str)
            
            current_date += timedelta(days=1)
        
        if not missing_dates:
            logger.info("没有发现缺失的指标数据")
            return {
                'success': True,
                'message': '没有缺失的指标数据',
                'retried_dates': 0
            }
        
        logger.info(f"发现 {len(missing_dates)} 个缺失日期: {missing_dates}")
        
        # 重试计算
        success_count = 0
        failed_dates = []
        
        for date in missing_dates:
            for attempt in range(self.max_retries):
                try:
                    result = self.update_specific_date(date, force=True)
                    if result['success']:
                        success_count += 1
                        logger.info(f"重试成功: {date}")
                        break
                    else:
                        logger.warning(f"重试失败 (尝试 {attempt+1}): {date} - {result.get('error', '未知错误')}")
                        if attempt < self.max_retries - 1:
                            time.sleep(self.retry_delay)
                except Exception as e:
                    logger.error(f"重试异常 (尝试 {attempt+1}): {date} - {e}")
                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
            else:
                failed_dates.append(date)
        
        return {
            'success': True,
            'retried_dates': len(missing_dates),
            'success_dates': success_count,
            'failed_dates': failed_dates
        }
    
    def get_update_status(self) -> Dict[str, Any]:
        """获取更新状态"""
        # 获取最新计算日期
        latest_calculated = self.storage.get_latest_date()
        
        # 获取最新原始数据日期
        latest_raw_date = None
        try:
            # 检查最近7天是否有原始数据
            for i in range(7):
                check_date = datetime.now() - timedelta(days=i)
                check_date_str = check_date.strftime('%Y-%m-%d')
                daily_data = self.data_service.load_daily_data(check_date_str)
                if not daily_data.empty:
                    latest_raw_date = check_date_str
                    break
        except Exception as e:
            logger.error(f"检查原始数据日期失败: {e}")
        
        # 计算滞后天数
        lag_days = 0
        if latest_calculated and latest_raw_date:
            calculated_date = datetime.strptime(latest_calculated, '%Y-%m-%d')
            raw_date = datetime.strptime(latest_raw_date, '%Y-%m-%d')
            lag_days = (raw_date - calculated_date).days
        
        # 获取股票数量
        symbols = self.storage.get_available_symbols()
        
        return {
            'latest_calculated_date': latest_calculated,
            'latest_raw_date': latest_raw_date,
            'lag_days': lag_days,
            'total_symbols': len(symbols),
            'needs_update': lag_days > 0
        }


def main():
    parser = argparse.ArgumentParser(description='增量更新股票技术指标')
    
    # 基本参数
    parser.add_argument('--workers', type=int, default=4, help='工作进程数')
    parser.add_argument('--symbols', help='股票代码，逗号分隔')
    
    # 更新模式
    parser.add_argument('--date', help='更新指定日期 (YYYY-MM-DD)')
    parser.add_argument('--days', type=int, help='更新最近N天')
    parser.add_argument('--target-date', help='更新到指定日期 (YYYY-MM-DD)')
    parser.add_argument('--retry', action='store_true', help='重试失败的日期')
    parser.add_argument('--status', action='store_true', help='显示更新状态')
    
    # 其他选项
    parser.add_argument('--force', action='store_true', help='强制重新计算')
    
    args = parser.parse_args()
    
    try:
        # 确保日志目录存在
        os.makedirs('logs', exist_ok=True)
        
        # 解析股票列表
        symbols = args.symbols.split(',') if args.symbols else None
        
        # 创建更新器
        updater = IncrementalUpdater(max_workers=args.workers)
        
        if args.status:
            # 显示状态
            status = updater.get_update_status()
            print("📊 更新状态:")
            print(f"  最新计算日期: {status['latest_calculated_date']}")
            print(f"  最新原始数据: {status['latest_raw_date']}")
            print(f"  滞后天数: {status['lag_days']}")
            print(f"  股票数量: {status['total_symbols']}")
            print(f"  需要更新: {'是' if status['needs_update'] else '否'}")
            
        elif args.retry:
            # 重试失败的日期
            print("🔄 重试失败的日期...")
            result = updater.retry_failed_dates()
            if result['success']:
                print(f"✅ 重试完成:")
                print(f"  重试日期: {result['retried_dates']}")
                print(f"  成功: {result['success_dates']}")
                if result.get('failed_dates'):
                    print(f"  仍然失败: {result['failed_dates']}")
            
        elif args.date:
            # 更新指定日期
            print(f"📅 更新指定日期: {args.date}")
            result = updater.update_specific_date(args.date, symbols, args.force)
            if result['success']:
                print(f"✅ 更新成功: {result.get('calculated', 0)} 只股票")
            else:
                print(f"❌ 更新失败: {result.get('error', '未知错误')}")
                
        elif args.days:
            # 更新最近N天
            print(f"📅 更新最近 {args.days} 天")
            result = updater.update_recent_days(args.days, symbols)
            if result['success']:
                print(f"✅ 更新完成:")
                print(f"  总日期: {result['total_dates']}")
                print(f"  成功: {result['calculated_dates']}")
                print(f"  失败: {result['failed_dates']}")
            else:
                print(f"❌ 更新失败: {result.get('error', '未知错误')}")
                
        else:
            # 默认：更新到最新
            print("🚀 增量更新到最新日期")
            result = updater.update_latest(args.target_date, symbols)
            if result['success']:
                if result.get('total_dates', 0) > 0:
                    print(f"✅ 更新完成:")
                    print(f"  总日期: {result['total_dates']}")
                    print(f"  成功: {result['calculated_dates']}")
                    print(f"  失败: {result['failed_dates']}")
                    print(f"  耗时: {result['elapsed_time']:.2f}s")
                else:
                    print(f"ℹ️ {result.get('message', '已经是最新数据')}")
            else:
                print(f"❌ 更新失败: {result.get('error', '未知错误')}")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        logger.error(f"执行失败: {e}")
        print(f"❌ 执行失败: {e}")


if __name__ == '__main__':
    main()
